<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\TypeCourse;

class CourseMother
{
    public static function create(
        ?int $id = null,
        string $name = 'Course 1',
        string $code = 'course-1',
        ?TypeCourse $typeCourse = null,
        ?string $description = 'Description course 1',
        string $locale = 'es',
        bool $active = true,
        bool $open = true,
        bool $isNew = true,
        bool $openVisible = true,
        ?CourseCategory $courseCategory = null,
        ?\DateTimeImmutable $createdAt = null,
        ?\DateTimeImmutable $updatedAt = null,
    ): Course {
        $course = new Course();

        if (null !== $id) {
            $course->setId($id);
        }

        $course->setName($name)
            ->setCode($code)
            ->setTypeCourse($typeCourse)
            ->setCategory($courseCategory)
            ->setLocale($locale)
            ->setDescription($description)
            ->setActive($active)
            ->setOpen($open)
            ->setIsNew($isNew)
            ->setOpenVisible($openVisible)
            ->setCreatedAt($createdAt ?? new \DateTimeImmutable())
            ->setUpdatedAt($updatedAt ?? new \DateTimeImmutable())
        ;

        return $course;
    }
}
