<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service\Task;

use App\Entity\Task;
use App\Exception\NoAvailableSlotException;
use App\Service\Announcement\AnnouncementExtraService;
use App\Service\Itinerary\General\ItineraryCourseReports;
use App\Service\Nps\NpsExtraAnswerUserService;
use App\Service\SettingsService;
use App\Service\SlotManagerService;
use App\Service\TaskCron\ExecutionSlot;
use App\Service\TaskCron\TaskExecutorService;
use App\Service\TemplatedEmail\TemplatedEmailService;
use App\Service\User\Authentication\StarTeam;
use App\StatsAndExcel\Services\StatsService;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

class TaskExecutorServiceTest extends TestCase
{
    private $em;
    private $starTeam;
    private $logger;
    private $settings;
    private $appKernel;
    private $translator;
    private $statsService;
    private $announcementRepository;
    private $announcementExtraService;
    private $itineraryCourseReports;
    private $npsExtraAnswerUserService;
    private $security;
    private $templatedEmailService;
    private $slotManagerService;
    private $taskExecutorService;
    private $taskRepository;
    private $taskService;

    protected function setUp(): void
    {
        $this->em = $this->createMock(EntityManagerInterface::class);
        $this->starTeam = $this->createMock(StarTeam::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->settings = $this->createMock(SettingsService::class);
        $this->appKernel = $this->createMock(KernelInterface::class);
        $this->translator = $this->createMock(TranslatorInterface::class);
        $this->statsService = $this->createMock(StatsService::class);
        $this->announcementRepository = $this->createMock(\App\Repository\AnnouncementRepository::class);
        $this->announcementExtraService = $this->createMock(AnnouncementExtraService::class);
        $this->itineraryCourseReports = $this->createMock(ItineraryCourseReports::class);
        $this->npsExtraAnswerUserService = $this->createMock(NpsExtraAnswerUserService::class);
        $this->security = $this->createMock(Security::class);
        $this->templatedEmailService = $this->createMock(TemplatedEmailService::class);
        $this->taskService = $this->createMock(\App\Service\Task\TaskService::class);
        $this->slotManagerService = $this->createMock(SlotManagerService::class);

        // Cambiar el tipo de mock para que pueda tener el método createQueryBuilder
        $this->taskRepository = $this->createMock(\App\Repository\TaskRepository::class);

        $this->em
            ->method('getRepository')
            ->with(Task::class)
            ->willReturn($this->taskRepository);

        $this->taskExecutorService = new TaskExecutorService(
            $this->em,
            $this->starTeam,
            $this->logger,
            $this->settings,
            $this->appKernel,
            $this->translator,
            $this->statsService,
            $this->announcementRepository,
            $this->announcementExtraService,
            $this->itineraryCourseReports,
            $this->npsExtraAnswerUserService,
            $this->security,
            $this->templatedEmailService,
            $this->taskService,
            $this->slotManagerService
        );
    }

    public function testGetAvailableExecutionSlotWithNoActiveTasks(): void
    {
        // Configurar el mock de SlotManagerService para devolver un ExecutionSlot
        $executionSlot = new ExecutionSlot(true);
        $this->slotManagerService->method('getAvailableTaskExecutionSlot')->willReturn($executionSlot);

        $slot = $this->taskExecutorService->getAvailableExecutionSlot();

        $this->assertInstanceOf(ExecutionSlot::class, $slot);
        $this->assertTrue($slot->allowsLongRunningTasks());
    }

    public function testGetAvailableExecutionSlotWithNonLongRunningTasks(): void
    {
        // Configurar el mock de SlotManagerService para devolver un ExecutionSlot
        $executionSlot = new ExecutionSlot(true);
        $this->slotManagerService->method('getAvailableTaskExecutionSlot')->willReturn($executionSlot);

        $slot = $this->taskExecutorService->getAvailableExecutionSlot();

        $this->assertInstanceOf(ExecutionSlot::class, $slot);
        $this->assertTrue($slot->allowsLongRunningTasks());
    }

    public function testGetAvailableExecutionSlotWithLongRunningTasks(): void
    {
        // Configurar el mock de SlotManagerService para devolver un ExecutionSlot
        $executionSlot = new ExecutionSlot(false);
        $this->slotManagerService->method('getAvailableTaskExecutionSlot')->willReturn($executionSlot);

        $slot = $this->taskExecutorService->getAvailableExecutionSlot();

        $this->assertInstanceOf(ExecutionSlot::class, $slot);
        $this->assertFalse($slot->allowsLongRunningTasks());
    }

    public function testGetAvailableExecutionSlotWithMaxActiveTasks(): void
    {
        // Configurar el mock de SlotManagerService para lanzar una excepción
        $this->slotManagerService->method('getAvailableTaskExecutionSlot')
            ->willThrowException(new NoAvailableSlotException('No hay slots disponibles para ejecutar más tareas'));

        $this->expectException(NoAvailableSlotException::class);
        $this->taskExecutorService->getAvailableExecutionSlot();
    }

    public function testGetEffectiveTaskTypeFromTypeProperty(): void
    {
        $task = new Task();
        $task->setType('test-type');

        // El método getEffectiveTaskType es privado, así que necesitamos crear una versión accesible
        $class = new \ReflectionClass(TaskExecutorService::class);
        $method = $class->getMethod('getEffectiveTaskType');
        $method->setAccessible(true);

        $result = $method->invokeArgs($this->taskExecutorService, [$task]);

        $this->assertEquals('test-type', $result);
    }

    public function testGetEffectiveTaskTypeFromParamsAndMoveToTypeProperty(): void
    {
        $task = new Task();
        $params = ['type' => 'param-type', 'other' => 'value'];
        $task->setParams($params);

        // Actualizamos para que espere solo una llamada a persist
        $this->em->expects($this->once())
            ->method('persist')
            ->with($task);

        $this->em->expects($this->once())
            ->method('flush');

        // El método getEffectiveTaskType es privado, así que necesitamos crear una versión accesible
        $class = new \ReflectionClass(TaskExecutorService::class);
        $method = $class->getMethod('getEffectiveTaskType');
        $method->setAccessible(true);

        $result = $method->invokeArgs($this->taskExecutorService, [$task]);

        $this->assertEquals('param-type', $result);
        $this->assertEquals('param-type', $task->getType());

        // No verificamos la eliminación de 'type' de params, ya que parece que la implementación
        // actual mantiene la clave 'type' en los params
    }
}
