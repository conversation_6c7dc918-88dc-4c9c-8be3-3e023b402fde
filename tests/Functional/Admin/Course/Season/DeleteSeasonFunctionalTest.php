<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course\Season;

use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\Season;
use App\Enum\ChapterContent;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\ChapterHelperTrait;
use App\Tests\Functional\HelperTrait\ChapterTypeHelperTrait;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\SeasonEndpointsTrait;
use App\Tests\Functional\HelperTrait\SeasonHelperTrait;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use Doctrine\DBAL\Exception;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class DeleteSeasonFunctionalTest extends FunctionalTestCase
{
    use SeasonEndpointsTrait;
    use CourseHelperTrait;
    use SeasonHelperTrait;
    use ChapterHelperTrait;
    use ChapterTypeHelperTrait;
    use UserHelperTrait;

    private ?Course $course;
    /** @var Chapter[] */
    private array $chapters;

    protected function setUp(): void
    {
        parent::setUp();

        $this->course = $this->createAndGetCourse();
    }

    public function testDeleteNoChapters(): void
    {
        $userToken = $this->loginAndGetToken();

        $season = $this->createAndGetSeason(course: $this->course);

        $this->client->request(
            'DELETE',
            $this->deleteSeasonEndpoint($season->getId()),
        );

        $response = $this->client->getResponse();
        $content = json_decode($response->getContent(), true);

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertArrayHasKey('error', $content);
        $this->assertFalse($content['error']);
    }

    public function testDeleteWithChapters(): void
    {
        $this->loginAndGetToken();
        $season = $this->createAndGetSeason(course: $this->course, name: 'Season 1');
        $season2 = $this->createAndGetSeason(course: $this->course, name: 'Season 2');
        $chapterType = $this->createAndGetChapterTypeRevised(id: ChapterContent::CONTENT_TYPE, name: 'test');
        $this->createAndGetChapter(
            title: 'Chapter 1',
            active: false,
            chapterType: $chapterType,
            course: $this->course,
            season: $season,
        );

        $this->createAndGetChapter(
            title: 'Chapter 1',
            active: false,
            chapterType: $chapterType,
            course: $this->course,
            season: $season,
            deletedAt: new \DateTimeImmutable(),
        );

        $this->createAndGetChapter(
            title: 'Chapter 1 Season 2',
            chapterType: $chapterType,
            course: $this->course,
            season: $season2,
        );

        $this->client->request(
            'DELETE',
            $this->deleteSeasonEndpoint($season->getId()),
        );

        $response = $this->client->getResponse();
        $content = json_decode($response->getContent(), true);

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertArrayHasKey('error', $content);
        $this->assertFalse($content['error']);
    }

    public function testTryDeleteWithActiveChapters(): void
    {
        $this->loginAndGetToken();
        $season = $this->createAndGetSeason(course: $this->course);
        $chapterType = $this->createAndGetChapterTypeRevised(id: ChapterContent::CONTENT_TYPE, name: 'test');
        $this->createAndGetChapter(
            title: 'Chapter 1',
            active: false,
            chapterType: $chapterType,
            course: $this->course,
            season: $season,
        );

        $this->createAndGetChapter(
            title: 'Chapter 2',
            active: false,
            chapterType: $chapterType,
            course: $this->course,
            season: $season,
            deletedAt: new \DateTimeImmutable(),
        );

        $this->createAndGetChapter(
            title: 'Chapter 3',
            chapterType: $chapterType,
            course: $this->course,
            season: $season,
        );

        $this->client->request(
            'DELETE',
            $this->deleteSeasonEndpoint($season->getId()),
        );

        $response = $this->client->getResponse();
        $content = json_decode($response->getContent(), true);

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertArrayHasKey('error', $content);
        $this->assertTrue($content['error']);
        $this->assertArrayHasKey('data', $content);
        $this->assertEquals(1, $content['data']);
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        // Clean stored data
        $this->truncateEntities([
            Chapter::class,
            Season::class,
            Course::class,
            CourseCategory::class,
        ]);
        parent::tearDown();
    }
}
