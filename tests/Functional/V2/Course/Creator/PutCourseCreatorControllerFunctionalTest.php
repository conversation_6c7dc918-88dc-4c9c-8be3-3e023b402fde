<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Course\Creator;

use App\Entity\Course;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseEndpoints;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Functional\V2\Fixtures\CourseCreatorFixtureTrait;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class PutCourseCreatorControllerFunctionalTest extends FunctionalTestCase
{
    use UserHelperTrait;
    use CourseHelperTrait;
    use CourseCreatorFixtureTrait;

    private array $usersIds = [];

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds($this->usersIds);
        parent::tearDown();
    }

    public function testBadRequestInvalidCourseId(): void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminCourseEndpoints::putCourseCreatorEndpoint(courseId: -1, userId: 1),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
    }

    public function testBadRequestInvalidUserId(): void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminCourseEndpoints::putCourseCreatorEndpoint(courseId: 1, userId: -1),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
    }

    public function testCourseNotFound(): void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminCourseEndpoints::putCourseCreatorEndpoint(courseId: 9999, userId: 1),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Course not found', $content['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testUserNotFound(): void
    {
        $course = $this->createAndGetCourse();
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminCourseEndpoints::putCourseCreatorEndpoint(courseId: $course->getId(), userId: 9999),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('User not found', $content['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testCreatorNotFound(): void
    {
        $course = $this->createAndGetCourse();

        // Create a user without ROLE_CREATOR
        $user = $this->createAndGetUser(
            firstName: 'Regular',
            lastName: 'User',
            roles: [User::ROLE_USER],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $user->getId();

        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminCourseEndpoints::putCourseCreatorEndpoint(courseId: $course->getId(), userId: $user->getId()),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('User is not a creator', $content['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testCreatorAlreadyExists(): void
    {
        $course = $this->createAndGetCourse();

        $creator = $this->createAndGetUser(
            firstName: 'Creator',
            lastName: 'User',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $creator->getId();

        // Add the creator to the course first
        $this->setAndGetCourseCreatorInRepository(
            userId: new Id($creator->getId()),
            courseId: new Id($course->getId()),
        );

        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminCourseEndpoints::putCourseCreatorEndpoint(courseId: $course->getId(), userId: $creator->getId()),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Creator is already associated with this course', $content['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testSuccessfulAddCreatorAsAdmin(): void
    {
        $course = $this->createAndGetCourse();

        $creator = $this->createAndGetUser(
            firstName: 'Creator',
            lastName: 'User',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $creator->getId();

        $userToken = $this->loginAndGetToken(); // Default user is admin

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminCourseEndpoints::putCourseCreatorEndpoint(courseId: $course->getId(), userId: $creator->getId()),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        $this->assertEmpty($response->getContent());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testSuccessfulAddCreatorAsOwner(): void
    {
        $courseOwner = $this->createAndGetUser(
            firstName: 'Owner',
            lastName: 'Creator',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $courseOwner->getId();

        $course = $this->createAndGetCourse(createdBy: $courseOwner);

        // Create another creator to add to the course
        $creator = $this->createAndGetUser(
            firstName: 'Creator',
            lastName: 'User',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $creator->getId();

        $ownerToken = $this->loginAndGetTokenForUser($courseOwner);

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminCourseEndpoints::putCourseCreatorEndpoint(courseId: $course->getId(), userId: $creator->getId()),
            bearerToken: $ownerToken,
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        $this->assertEmpty($response->getContent());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testForbiddenCreatorNotOwner(): void
    {
        // Create a creator user who will be the course owner
        $courseOwner = $this->createAndGetUser(
            firstName: 'Owner',
            lastName: 'Creator',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $courseOwner->getId();

        // Create course with the creator as owner
        $course = $this->createAndGetCourse(createdBy: $courseOwner);

        // Create another creator who is NOT the owner
        $notOwnerCreator = $this->createAndGetUser(
            firstName: 'NotOwner',
            lastName: 'Creator',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $notOwnerCreator->getId();

        // Create the creator to add
        $creatorToAdd = $this->createAndGetUser(
            firstName: 'Creator',
            lastName: 'User',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $creatorToAdd->getId();

        // Login as the creator who is NOT the owner
        $notOwnerToken = $this->loginAndGetTokenForUser($notOwnerCreator);

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminCourseEndpoints::putCourseCreatorEndpoint(
                courseId: $course->getId(),
                userId: $creatorToAdd->getId()
            ),
            bearerToken: $notOwnerToken,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals(
            'User ' . (string) $notOwnerCreator . ' is not authorized to perform this action for course ' . (string) $course,
            $content['message']
        );
    }

    public function testUnauthorizedWithoutToken(): void
    {
        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminCourseEndpoints::putCourseCreatorEndpoint(courseId: 1, userId: 1),
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }
}
