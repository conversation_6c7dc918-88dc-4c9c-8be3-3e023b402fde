<?php

declare(strict_types=1);

namespace App\Service\StatsUser\Games;

use App\Campus\Service\UserCourseChapter\UserCourseService;
use App\Entity\Categorize;
use App\Entity\CategorizeAnswers;
use App\Entity\UserCourseChapter;
use App\Service\SettingsService;
use App\Service\StatsUser\Games\Traits\ScoreAttempGameTrait;
use Doctrine\ORM\EntityManagerInterface;

class Categorized implements GamesStragegyResultInterface
{
    use ScoreAttempGameTrait;

    private $em;
    private $settings;
    private UserCourseService $userCourseService;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        UserCourseService $userCourseService
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->userCourseService = $userCourseService;
    }

    public function getResultGame(UserCourseChapter $userCourseChapter): array
    {
        $attempts = [];
        $categorizeRepository = $this->em->getRepository(Categorize::class);

        if (!empty($userCourseChapter->getData()['attempts'])) {
            foreach ($userCourseChapter->getData()['attempts'] as $key => $attempt) {
                $questions = [];
                $lastDateInQuestions = null;
                $timeTotalAttempt = 0;

                if (empty($attempt)) {
                    continue;
                }

                foreach ($attempt as $question) {
                    $questionFind = $categorizeRepository->find($question['questionId']);
                    $timeInQuestion = $question['time'] ? ceil($question['time']) : 0;

                    if ($questionFind) {
                        $questions[] = [
                            'id' => $questionFind->getId(),
                            'question' => $questionFind->getQuestion(),
                            'correct' => $question['correct'] ?? false,
                            'answers' => $questionFind->getCategorizeAnswers() ? $this->getAnswers($questionFind, $question) : [],
                        ];
                        $lastDateInQuestions = $question['date'] ?? $userCourseChapter->getUpdatedAt()->format('Y-m-d H:i:s');
                    }

                    $timeTotalAttempt += $timeInQuestion ?? 0;
                }

                if (!$questions) {
                    continue;
                }

                $attemptForCalculateScore = [
                    'answers' => $attempt,
                ];

                $attempts[] = [
                    'attempt' => $key + 1,
                    'timeTotal' => ceil($timeTotalAttempt),
                    'date' => $lastDateInQuestions,
                    'state' => $this->getStateGameAttemp($userCourseChapter, $this->userCourseService, $attemptForCalculateScore),
                    'questions' => $questions,
                ];
            }
        }

        return $attempts;
    }

    private function getAnswers(Categorize $categorize, $answerInTheGame): array
    {
        $answers = [];
        if (!$categorize->getCategorizeAnswers()) {
            return [];
        }
        if (!$answerInTheGame) {
            return [];
        }

        $answerUser = $this->getAnswerUser($answerInTheGame['id'] ?? null);
        if (!$answerUser) {
            return [];
        }

        foreach ($categorize->getCategorizeAnswers() as $answer) {
            $isUserAnswer = $answerUser['id'] == $answer->getId();
            $incorrect = $isUserAnswer && !$answer->isCorrect();

            $answers[] = [
                'answer' => $answer->getOptions()->getName(),
                'correct' => $answer->isCorrect(),
                'incorrect' => $incorrect,
            ];
        }

        return $answers;
    }

    private function getAnswerUser($idAnswer): array
    {
        if (!$idAnswer) {
            return [];
        }
        $answer = $this->em->getRepository(CategorizeAnswers::class)->find($idAnswer);

        return [
            'id' => $answer->getId(),
            'answer' => $answer->getOptions()->getName(),
            'imageUrl' => $answer->getOptions()->getImageUrl(),
        ];
    }
}
