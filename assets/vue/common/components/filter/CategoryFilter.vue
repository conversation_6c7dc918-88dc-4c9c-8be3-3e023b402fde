<template>
  <div v-if="loadingCategories" class="d-flex align-items-center justify-content-center"><spinner /></div>
  <div class="CategoryFilter" v-else>
    <nav class="CategoryFilter--categories mt-3 mb-2">
      <div class="nav" id="nav-tab">
        <button
            type="button"
            v-for="category in categories"
            :class="['btn', {'text-primary selected' : categoryId === category.id}]"
            @click="categoryId = category.id"
        >
          {{ category.name }} <span v-if="!showWarning && showCategoryWarningStatus">({{ category.selected > 0 ? category.selected : 'Todos' }})</span>
        </button>
      </div>
    </nav>
    <div class="w-100 m-1 text-center" v-if="showCategoryWarningStatus && showWarning">
      <p class="text-danger font-weight-bold">{{ categoryWarningStatusText }}</p>
    </div>
    <div class="CategoryFilter--content" :class="allowAll ? 'all' : ''">
      <div class="CategoryFilter--content--available">
        <div class="title" v-if="showTitles">
          <span>{{ $t('CATEGORY_FILTER.AVAILABLE') }}</span>
        </div>
        <div class="content">
          <div class="content--filter form-group btn-right">
            <input type="text" class="form-control" v-model="queryAvailable">
            <button type="button"
                    @click="addAll()"
                    class="btn btn-primary">{{ $t('ADD_ALL') }} <i class="fa fa-angle-double-right"></i></button>
          </div>
          <div class="content--loader" v-if="loadingAvailable">
            <spinner />
          </div>
          <div class="content--content" v-else>
            <div class="card" v-for="filter in availableFiltered" :key="filter.id">
              <div class="card--information">
                <span class="card--information--title">{{ filter.name }}</span>
              </div>
              <button type="button" class="ml-auto btn text-primary" @click="add(filter)"><i class="fa fa-plus"></i></button>
            </div>
          </div>
        </div>
      </div>
<!--      <div class="CategoryFilter&#45;&#45;content&#45;&#45;actions d-flex flex-column p-0" v-if="allowAll">-->
<!--        <button type="button" class="btn btn-sm btn-primary w-100" @click="addAll()"><i class="fa fa-angle-double-right"></i></button>-->
<!--        <button type="button" class="btn btn-sm btn-default w-100 mt-1" @click="removeAll()"><i class="fa fa-angle-double-left"></i></button>-->
<!--      </div>-->
      <div class="CategoryFilter--content--selected">
        <div class="title" v-if="showTitles">
          <span>{{ $t('CATEGORY_FILTER.SELECTED') }}</span>
        </div>
        <div class="content">
          <div class="content--filter form-group btn-left">
            <button type="button"
                    class="btn btn-primary"
                    @click="removeAll()">
              <i class="fa fa-angle-double-left"></i> {{ $t('REMOVE_ALL') }}
            </button>
            <input type="text" class="form-control" v-model="querySelected" placeholder="">
          </div>
          <div class="content--loader" v-if="loadingSelected">
            <spinner />
          </div>
          <div class="content--content" v-else>
            <div class="card" v-for="filter in selectedFiltered" :key="filter.id">
              <div class="card--information">
                <span class="card--information--title">{{ filter.name }}</span>
              </div>
              <button type="button" class="ml-auto btn text-danger" @click="remove(filter)"><i class="fa fa-minus"></i></button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import Spinner from "../../../admin/components/base/Spinner.vue";
import axios from "axios";

export function generateCompatibleData(content)
{
  const data = {};
  content.forEach(i => {
    if ('categoryId' in i)
    {
      if(!data[`category_${i.categoryId}`])
      {
        data[`category_${i.categoryId}`] = [];
      }
      data[`category_${i.categoryId}`].push(i);
    }
  });
  return data;
}

export function getFiltersForRequest(content) {
  const keys = Object.keys(content);
  const filters = [];
  keys.forEach(key => {
    content[key].forEach(filter => {
      filters.push(filter.id);
    })
  })
  return filters;
}

export default{
  name: "CategoryFilter",
  components: {Spinner},
  props: {
    showTitles: {
      type: Boolean,
      default: false
    },
    titleAvailable: {
      type: String,
      default: 'FILTER_CATEGORY.AVAILABLE'
    },
    titleSelected: {
      type: String,
      default: 'FILTER_CATEGORY.SELECTED'
    },

    urlCategories: {
      type: String,
      default: '/admin/filter/categories'
    },

    urlAvailableFilters: {
      type: String,
      default: '/admin/filter/list-with-category'
    },

    realtime: {
      type: Boolean,
      default: false
    },

    rest: {
      type: Boolean,
      default: false
    },

    urlAdd: {
      type: String,
      default: null
    },
    urlAddAll: {
      type: String,
      default: null
    },
    urlRemove: {
      type: String,
      default: null
    },
    urlRemoveAll: {
      type: String,
      default: null
    },

    allowAll: {
      type: Boolean,
      default: true
    },

    /**
     * @param showCategoryStatus Used whether to display or not warning related to the multiple selections
     */
    showCategoryWarningStatus: {
      type: Boolean,
      default: false,
    },

    /**
     * @param categoryWarningStatusText Pass translated warning status text
     */
    categoryWarningStatusText: {
      type: String,
      default: null
    },

    /**
     * @param value Pass the current status as v-model
     * format: {
     *   category_1: [
     *     {
     *       id: 1,
     *       name: 'Filter Name
     *     }
     *   ]
     * }
     * where the index is the category id
     */
    value: null,
  },
  data() {
    return {
      loadingCategories: false,
      loadingAvailable: false,
      loadingSelected: false,

      categoryId: null,
      queryAvailable: '',
      querySelected: '',

      categories: [],//All available categories
      filters: [],// All available filters

      selectedOffline: [],
      validated: false,
      showWarning: true
    };
  },

  computed: {
    selectedCategoryId: function () {
      return `category_${this.categoryId}`;
    },

    available: function() {
      const selected = this.selected ?? [];
      const available = this.filters[this.categoryId] ?? [];
      if (selected.length === 0) return available;

      return available.filter(filter => {
        const index = selected.findIndex(item => item.id === filter.id);
        return index < 0;
      })
    },

    selected: function () {
      if (!this.innerValue) return [];
      return this.innerValue[`category_${this.categoryId}`] ?? [];
    },

    innerValue: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    },

    selectedFiltered: function () {
      const selected = this.selected;
      let query = this.querySelected;
      if (query.length === 0) return selected;
      if (selected[`category_${this.categoryId}`] === undefined) return [];
      query = this.normalize(query);
      return selected.filter(filter => this.normalize(filter.name).includes(query));
    },

    availableFiltered: function () {
      let query = this.queryAvailable;
      if (query.length === 0) return this.available;
      query = this.normalize(query);
      return this.available.filter(filter => this.normalize(filter.name).includes(query));
    }
  },

  watch: {
    innerValue: {
      handler: function (val, oldVal) {
        this.validateWarning();
      },
      immediate: true
    }
  },

  async mounted() {
    this.loadingCategories = true;
    this.loadingAvailable = true;
    this.loadingSelected = true;

    try {
      await this.loadCategories();
      await this.loadFilters();
      await this.validateSelected();
    } finally {
      this.loadingCategories = false;
      this.loadingAvailable = false;
      this.loadingSelected = false;
    }
    this.validateWarning();
  },

  methods: {
    validateWarning() {
      let warning = true;
      const categories = structuredClone(this.categories);
      for (let i = 0; i < this.categories.length; i++) {
        const size = this.innerValue['category_' + categories[i].id]?.length ?? 0;
        if (size > 0) warning = false;
        categories[i]['selected'] = size;
      }
      this.showWarning = warning;
      this.categories = categories;
    },
    async add(filter) {
      let success = !this.realtime;
      if (this.realtime) {
        const self = this;
        function save() {
          if (self.rest) return axios.post(`${self.urlAdd}/${filter.id}`);
          else return axios.post(self.urlAdd, filter);
        }

        const result = await save();
        const { error } = result.data;
        success = !error;
        if (error) this.$toast.error(this.$t(`CATEGORY_FILTER.ADD.FAILED`, [filter.name]) + '');
        else this.$toast.error(this.$t(`CATEGORY_FILTER.ADD.SUCCESS`, [filter.name]) + '');
      }
      if (success) this.addElement(filter);
    },

    async addAll() {
      const elements = structuredClone(this.availableFiltered);
      let success = !this.realtime;
      if (this.realtime) {
        const ids = [];
        elements.forEach(item => {
          ids.push(item.id);
        });

        const result = await axios.post(this.urlAddAll, { data: ids});
        const { error } = result.data;
        success = !error
        if (success) this.$toast.success(this.$t('CATEGORY_FILTER.ADD_ALL.SUCCESS') + '');
        else this.$toast.error(this.$t('CATEGORY_FILTER.ADD_ALL.ERROR') + '')
      }

      if (success) {
        const selected = structuredClone(this.innerValue);
        elements.forEach(item => {
          selected[`category_${this.categoryId}`].push(item);
        });
        this.innerValue = selected;
      }
    },

    async remove(filter) {
      let success = !this.realtime;
      if (this.realtime) {
        const self = this;
        function save() {
          if (self.rest) return axios.delete(`${self.urlRemove}/${filter.id}`);
          else return axios.post(self.urlAdd, filter);
        }

        const result = await save();
        const { error } = result.data;
        success = !error;
        if (error) this.$toast.error(this.$t(`CATEGORY_FILTER.REMOVE.FAILED`, [filter.name]) + '');
        else this.$toast.error(this.$t(`CATEGORY_FILTER.REMOVE.SUCCESS`, [filter.name]) + '');
      }
      if (success) this.removeElement(filter);
    },

    async removeAll() {
      const elements = structuredClone(this.selectedFiltered);
      if (elements.length === 0) return;
      let success = !this.realtime;
      if (this.realtime) {
        const ids = [];
        elements.forEach(item => {
          ids.push(item.id);
        });
        const result = await axios.post(this.urlRemoveAll, { data: ids });
        const { error } = result.data;
        success = !error;
        if (success) this.$toast.success(this.$t('CATEGORY_FILTER.REMOVE_ALL.SUCCESS') + '');
        else this.$toast.error(this.$t('CATEGORY_FILTER.REMOVE_ALL.FAILED') + '');
      }

      if (success) {
        const selected = structuredClone(this.innerValue);
        elements.forEach(item => {
          const index = selected[`category_${this.categoryId}`].findIndex(s => s.id === item.id);
          if (index >= 0) {
            selected[`category_${this.categoryId}`].splice(index, 1);
          }
        });
        this.innerValue = selected;
      }
    },

    addElement(filter) {
      const selected = structuredClone(this.innerValue);
      if (!selected[`category_${this.categoryId}`]) selected[`category_${this.categoryId}`] = [];
      selected[`category_${this.categoryId}`].push(filter);
      this.innerValue = selected;
    },

    removeElement(filter) {
      const selected = structuredClone(this.innerValue);
      const index = selected[`category_${this.categoryId}`].findIndex(s => s.id === filter.id);
      if (index >= 0) {
        selected[`category_${this.categoryId}`].splice(index, 1);
        this.innerValue = selected;
      }
    },

    async loadCategories() {
      this.loadingCategories = true;
      try {
        const result = await axios.get(this.urlCategories);
        const { data } = result.data;
        this.categories = data;
        if (this.categories.length > 0) this.categoryId = this.categories[0].id;
      } finally {
        this.loadingCategories = false;
      }
    },

    async loadFilters() {
      try {
        const result = await axios.get(this.urlAvailableFilters);
        const { data } = result.data;
        this.filters = data;
      } finally {

      }
    },

    normalize(string) {
      return string.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, "")
    },

    initSelected() {
      const selected = this.innerValue;
      this.categories.forEach(category => {
        if (!selected[`category_${category.id}`])
          selected[`category_${category.id}`] = [];
      });
      return selected;
    },

    validateSelected() {
      const allFilters = this.innerValue ?? [];
      let selected = this.initSelected();

      // Check if the content needs rework
      if (allFilters.length > 0 && 'categoryId' in allFilters[0]) {
        allFilters.forEach(filter => {
          selected[`category_${filter.categoryId}`].push({
            id: filter.id,
            name: filter.name
          });
        });
      }
      else if (allFilters.length > 0) {
        selected = allFilters;
      }
      this.innerValue = selected;
    }
  },
};
</script>

<style scoped lang="scss">
$available-card-border-color: #CBD5E1;
$selected-card-border-color: #13C8FF;
.CategoryFilter {
  &--categories {
    .nav {
      justify-content: flex-end;
      gap: 0.15rem;
      button {
        border-radius: 0.25rem 0.25rem 0 0;
        border: 1px solid var(--color-neutral-mid);
        background-color: var(--color-neutral-lighter);
        &.selected {
          border: 1px solid var(--color-neutral-mid) !important;
          border-bottom: 0 !important;
          background-color: #ffffff;
        }
      }
    }
  }

  &--content {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    background-color: #ffffff;
    justify-content: center;

    .content--filter {
      width: 100%;
      display: grid;
      gap: .5rem;
      &.btn-right {
        grid-template-columns: 1fr 150px;
      }
      &.btn-left {
        grid-template-columns: 150px 1fr;
      }
    }

    &--actions {
      align-self: center;
    }

    &--available, &--selected {
      display: flex;
      flex-flow: column;
      padding: 0.5rem;

      .title {
        font-weight: bold;
        color: var(--color-neutral-darkest);
        font-size: 18px;
        text-align: center;
        width: 100%;
      }

      .content {
        padding: 0.5rem;
        background: var(--color-neutral-lighter);
        &--content {
          display: grid;
          gap: 0.25rem;
          align-content: flex-start;
          height: 50vh;
          overflow-y: auto;
          overflow-x: hidden;

          .card {
            padding: 0.75rem;
            flex-direction: row;
            align-items: center;
            box-shadow: none;
            border-radius: 5px;

            &--information {
              display: grid;
              flex: 1;
              gap: 0.125rem;
              font-size: .9rem;
              &--title {
                font-weight: 500;
              }
            }
          }
        }
      }
    }

    &--available {
      .card {
        border: 1px solid $available-card-border-color;
      }
    }

    &--selected {
      .card {
        border: 1px solid $selected-card-border-color;
      }
    }
  }
}
</style>
