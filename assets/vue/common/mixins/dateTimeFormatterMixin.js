export default {
    computed: {
        clientTimezone() {
            return Intl.DateTimeFormat().resolvedOptions().timeZone;
        }
    },
    methods: {
        getDateTimeFormatted(dateValue, locale = 'es-ES', pOptions = {
            timezone: null,
            hour12: false
        }) {
            const dateObject = new Date(dateValue);

            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: pOptions?.hour12??false
            };

            if (pOptions?.timezone != null) options.timeZone = pOptions.timezone;

            return dateObject.toLocaleDateString(locale, options);
        },

        formatHoursToTime(hoursDecimal) {            
            const totalMinutes = Math.round(hoursDecimal * 60);
            const hours = Math.floor(totalMinutes / 60);
            const minutes = totalMinutes % 60;      
           
            return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
              2,
              "0"
            )}`;
          },
    }
}
