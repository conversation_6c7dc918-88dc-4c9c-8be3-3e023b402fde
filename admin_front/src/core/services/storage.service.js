import StorageUtils from '@/core/utils/storage.utils.js'
import { ONE_DAY_IN_MS } from '@/core/constants/general.constant.js'

const STORAGE_KEYS = {
  TOKEN: 'token',
  REFRESH_TOKEN: 'refresh-token',
  LOCALE: 'locale',
  LOCALE_LIST: 'locales',
}

class StorageService {
  static setToken(value = '') {
    StorageUtils.setValue(STORAGE_KEYS.TOKEN, value, ONE_DAY_IN_MS)
  }
  static getToken() {
    return StorageUtils.getValue(STORAGE_KEYS.TOKEN)
  }
  static setRefreshToken(value = '') {
    StorageUtils.setValue(STORAGE_KEYS.REFRESH_TOKEN, value, ONE_DAY_IN_MS)
  }
  static getRefreshToken() {
    return StorageUtils.getValue(STORAGE_KEYS.REFRESH_TOKEN)
  }
  static setLocale(value = '') {
    StorageUtils.setValue(STORAGE_KEYS.LOCALE, value, ONE_DAY_IN_MS)
  }
  static getLocale() {
    return StorageUtils.getValue(STORAGE_KEYS.LOCALE)
  }
  static setLocaleList(value = '') {
    StorageUtils.setValue(STORAGE_KEYS.LOCALE_LIST, value, ONE_DAY_IN_MS)
  }
  static getLocaleList() {
    return StorageUtils.getValue(STORAGE_KEYS.LOCALE_LIST)
  }
}

export default StorageService
