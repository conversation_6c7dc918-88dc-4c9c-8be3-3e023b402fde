<template>
  <div class="UsersBasicInfo">
    <div class="avatarContainer">
      <img
        class="userAvatar"
        :src="innerValue.avatar"
        :alt="innerValue.fullName + ' avatar'"
      />
    </div>
    <div class="form-data">
      <BaseInput
        v-model="innerValue.firstName"
        :label="$t('NAME')"
        name="firstName"
        :error="innerValue.errors?.name"
        required
      />
      <BaseInput
        v-model="innerValue.lastName"
        :label="$t('SUBSCRIPTION.LAST_NAME')"
        :error="innerValue.errors?.last_name"
        name="lastName"
        required
      />
      <BaseInput
        v-model="innerValue.email"
        :label="$t('USER.EMAIL')"
        :error="innerValue.errors?.email"
        name="email"
        required
      />
      <BaseSelect
        v-model="innerValue.language"
        :label="$t('USERS.FORM.LOCALE')"
        :error="innerValue.errors?.language"
        :options="localeList"
        name="language"
        required
      />
      <BaseInput
        v-model="innerValue.code"
        :label="$t('CODE')"
        :error="innerValue.errors?.code"
        name="code"
        required
      />
      <BaseSelect
        v-model="innerValue.company"
        :label="$t('USER.LABEL.COMPANY')"
        name="email"
        :options="companyList"
        :error="innerValue.errors?.company"
        required
      />
      <BaseSelect
        v-model="innerValue.zone"
        :label="$t('TIMEZONE')"
        :options="timezoneList"
        :error="innerValue.errors?.zone"
        name="zone"
        required
      />
      <BaseInput
        v-model="innerValue.dni"
        :label="$t('USER.USER_FIELDS_FUNDAE.DNI')"
        :error="innerValue.errors?.dni"
        name="dni"
        required
      />
      <div class="openCampus">
        <BaseSwitch
          v-model="innerValue.openCampus"
          name="openCampus"
        />
        {{ $t('USER.LABEL.OPEN_CAMPUS') }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { UserFormModel } from '@/contexts/users/models/userForm.model.js'
import BaseInput from '@/contexts/shared/components/BaseInput.vue'
import { computed } from 'vue'
import BaseSelect from '@/contexts/shared/components/BaseSelect.vue'
import BaseSwitch from '@/contexts/shared/components/BaseSwitch.vue'

const emit = defineEmits(['update:modelValue'])
const props = defineProps({
  modelValue: { type: [Object, UserFormModel], default: () => new UserFormModel() },
  companyList: { type: Array, default: () => [] },
  timezoneList: { type: Array, default: () => [] },
  localeList: { type: Array, default: () => [] },
})
const innerValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})
</script>

<style scoped lang="scss">
.UsersBasicInfo {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: start;

  .avatarContainer {
    width: 300px;

    img {
      width: 12rem;
      margin: 0 auto;
    }
  }

  .form-data {
    flex-grow: 1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(310px, 1fr));
    gap: 2rem;
    padding: 0 1rem;

    .BaseInput {
      width: 100%;
    }

    .openCampus {
      display: flex;
      gap: 1rem;
    }
  }
}
</style>
