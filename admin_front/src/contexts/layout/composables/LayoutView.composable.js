import { computed, provide, ref } from 'vue'
import { useRouter } from 'vue-router'
import { LoaderService } from '@/core/services/loader.service.js'
import { useAuthStore } from '@/contexts/shared/stores/auth.store.js'
import { useAuth } from '@/contexts/shared/composables/auth.composable.js'
import { useLocalStore } from '@/core/stores/locale.store.js'
import { storeToRefs } from 'pinia'

export function useLayoutView() {
  const backgroundImage = computed(() => `url("${LoaderService.importImage('background')}")`)
  const submitLogout = ref(false)
  async function logout() {
    const { logout } = useAuthStore()
    if (submitLogout.value) {
      return null
    }
    submitLogout.value = true
    await logout()

    const { isAuth } = useAuth()
    if (!isAuth.value) window.location.reload()
    submitLogout.value = false
  }

  const router = useRouter()
  function openRoute(route = { name: '', params: {} }) {
    if (!route.name) {
      return null
    }
    if (/http(s)?:/gi.test(route.name) || route.name.includes('ea-route'))
      return window.open(route.name, '_self', 'noopener noreferrer')
    try {
      router.push(route).catch()
    } catch (e) {}
  }

  const { locales } = storeToRefs(useLocalStore())
  const localeOptions = computed(() => ({
    admin: Object.entries(locales.value?.admin || locales.value).map(([value, label]) => ({ label, value })),
    campus: Object.entries(locales.value?.campus || locales.value).map(([value, label]) => ({ label, value })),
  }))

  provide('LayoutEmits', { logout, openRoute })
  provide('LayoutProps', { localeOptions })

  return {
    backgroundImage,
  }
}
