<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\RouletteWord;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method RouletteWord|null find($id, $lockMode = null, $lockVersion = null)
 * @method RouletteWord|null findOneBy(array $criteria, array $orderBy = null)
 * @method RouletteWord[]    findAll()
 * @method RouletteWord[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RouletteWordRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RouletteWord::class);
    }
}
