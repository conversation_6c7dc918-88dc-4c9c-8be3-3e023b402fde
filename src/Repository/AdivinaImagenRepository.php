<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AdivinaImagen;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AdivinaImagen>
 *
 * @method AdivinaImagen|null find($id, $lockMode = null, $lockVersion = null)
 * @method AdivinaImagen|null findOneBy(array $criteria, array $orderBy = null)
 * @method AdivinaImagen[]    findAll()
 * @method AdivinaImagen[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AdivinaImagenRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AdivinaImagen::class);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function add(AdivinaImagen $entity, bool $flush = true): void
    {
        $this->_em->persist($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function remove(AdivinaImagen $entity, bool $flush = true): void
    {
        $this->_em->remove($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }
}
