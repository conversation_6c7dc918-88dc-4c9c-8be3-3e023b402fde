<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\FilesManager;
use App\Entity\FilesManagerExtra;
use App\Entity\User;
use App\Entity\ZipFileTask;
use App\Service\Annoucement\CronJob\ZipFileTaskService;
use App\Service\SlotManagerService;
use App\Utils\ZipFile;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Response;

/**
 * @extends ServiceEntityRepository<ZipFileTask>
 *
 * @method ZipFileTask|null find($id, $lockMode = null, $lockVersion = null)
 * @method ZipFileTask|null findOneBy(array $criteria, array $orderBy = null)
 * @method ZipFileTask[]    findAll()
 * @method ZipFileTask[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ZipFileTaskRepository extends ServiceEntityRepository
{
    private string $zipFilesDirPath;
    private SlotManagerService $slotManagerService;
    private EntityManagerInterface $em;
    private LoggerInterface $logger;

    public function __construct(
        ManagerRegistry $registry,
        ParameterBagInterface $params,
        SlotManagerService $slotManagerService,
        EntityManagerInterface $em,
        LoggerInterface $logger
    ) {
        $this->zipFilesDirPath = ZipFile::checkPath($params->get('app.file_manager.base_dir'), ZipFileTaskService::ZIP_PATH);
        $this->slotManagerService = $slotManagerService;
        $this->em = $em;
        $this->logger = $logger;
        parent::__construct($registry, ZipFileTask::class);
    }

    public function add(ZipFileTask $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ZipFileTask $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Creates a new ZipFileTask.
     *
     * @param string          $task         Task type
     * @param string|int|null $entityId     Related entity ID
     * @param string|null     $type         Task type
     * @param array           $params       Additional parameters
     * @param string|null     $originalName Original file name
     * @param object|null     $user         User who created the task
     */
    public function newZipFileTask(
        string $task,
        $entityId = null,
        ?string $type = null,
        array $params = [],
        ?string $originalName = null,
        ?object $user = null
    ): ZipFileTask {
        $zipTask = new ZipFileTask();
        $zipTask->setType($type)
            ->setEntityId(null !== $entityId ? (string) $entityId : null)
            ->setTask($task)
            ->setOriginalName($originalName ?: 'export-' . date('Y-m-d-H-i-s'))
            ->setParams([
                'params' => $params
            ]);

        if ($user) {
            $zipTask->setCreatedBy($user);
        }

        $this->em->persist($zipTask);
        $this->em->flush();

        return $zipTask;
    }

    public function download(ZipFileTask $task): Response
    {
        $filePath = ZipFile::checkPath($this->zipFilesDirPath);

        return ZipFile::downloadZipFromPath($filePath, $task->getFilename(), $task->getOriginalName());
    }

    public function generateFilesManagerFile(ZipFileTask $task): ?FilesManager
    {
        $user = $task->getCreatedBy();
        $fullPath = ZipFile::checkPath($this->zipFilesDirPath, null, $task->getFilename());
        $filesManager = $task->getFilesManager();
        if (!$filesManager && file_exists($fullPath)) {
            $filesManager = new FilesManager();
            $filesManager->setCreatedBy($user)
                ->setFilename($task->getFilename())
                ->setOriginalName($task->getOriginalName())
                ->setFileSize(filesize($fullPath))
                ->setMimeType('application/zip')
                ->setRequiredRole('ROLE_USER')
                ->setFilePath(ZipFileTaskService::ZIP_PATH);

            $this->em->persist($filesManager);

            // Extra data
            $filesManagerExtra = $this->em->getRepository(FilesManagerExtra::class)->findOneBy([
                'entityId' => $task->getEntityId(),
                'type' => $task->getType()
            ]);
            if (!$filesManagerExtra) {
                $filesManagerExtra = new FilesManagerExtra();
                $filesManagerExtra->setEntityId($task->getEntityId())
                    ->setType($task->getType());
            }
            $filesManagerExtra->addFilesManager($filesManager);
            $this->em->persist($filesManagerExtra);
            $this->em->flush();
        }

        return $filesManager;
    }

    /**
     * Counts the number of pending ZipFileTasks for a user.
     * Tasks with STATUS_TIMEOUT and startedAt more than 1 hour ago are not considered pending.
     */
    public function countPendingZipFileTasksByUser(?User $user): int
    {
        try {
            if (!$user) {
                return 0;
            }

            $qb = $this->createQueryBuilder('z')
                ->select('COUNT(z.id)')
                ->where('z.createdBy = :user')
                ->andWhere('z.deletedAt IS NULL')
                ->andWhere('z.status IN (:pendingStatuses)')
                ->setParameter('user', $user)
                ->setParameter('pendingStatuses', [
                    ZipFileTask::STATUS_PENDING,
                    ZipFileTask::STATUS_IN_PROGRESS
                ]);

            // Add timeout tasks that started less than the configured timeout period ago
            $timeoutSeconds = $this->slotManagerService->getTaskTimeoutSeconds();
            $timeoutAgo = new \DateTimeImmutable('-' . $timeoutSeconds . ' seconds');
            $qb->orWhere('z.createdBy = :user AND z.deletedAt IS NULL AND z.status = :timeoutStatus AND z.startedAt IS NOT NULL AND z.startedAt > :timeoutAgo')
                ->setParameter('timeoutStatus', ZipFileTask::STATUS_TIMEOUT)
                ->setParameter('timeoutAgo', $timeoutAgo);

            return (int) $qb->getQuery()->getSingleScalarResult();
        } catch (\Exception $e) {
            $this->logger->error('[src/Repository/ZipFileTaskRepository.php] countPendingZipFileTasksByUser() error: ' . $e->getMessage());
            return 0;
        }
    }
}
