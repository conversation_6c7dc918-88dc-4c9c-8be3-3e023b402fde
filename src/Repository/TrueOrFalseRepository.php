<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\TrueOrFalse;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method TrueOrFalse|null find($id, $lockMode = null, $lockVersion = null)
 * @method TrueOrFalse|null findOneBy(array $criteria, array $orderBy = null)
 * @method TrueOrFalse[]    findAll()
 * @method TrueOrFalse[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TrueOrFalseRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TrueOrFalse::class);
    }
}
