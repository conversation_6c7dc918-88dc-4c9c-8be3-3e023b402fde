<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Guessword;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Guessword>
 *
 * @method Guessword|null find($id, $lockMode = null, $lockVersion = null)
 * @method Guessword|null findOneBy(array $criteria, array $orderBy = null)
 * @method Guessword[]    findAll()
 * @method Guessword[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class GuesswordRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Guessword::class);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function add(Guessword $entity, bool $flush = true): void
    {
        $this->_em->persist($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function remove(Guessword $entity, bool $flush = true): void
    {
        $this->_em->remove($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }
}
