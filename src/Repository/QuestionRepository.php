<?php

namespace App\Repository;

use App\Admin\Traits\StatsQueryFiltersTrait;
use App\Entity\Chapter;
use App\Entity\Question;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Question|null find($id, $lockMode = null, $lockVersion = null)
 * @method Question|null findOneBy(array $criteria, array $orderBy = null)
 * @method Question[]    findAll()
 * @method Question[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class QuestionRepository extends ServiceEntityRepository
{
    use StatsQueryFiltersTrait;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Question::class);
    }

     /**
     * @param Chapter $chapter
     * @return Question[]
     */

    public function findByChapterId(Chapter $chapter)
    {
        return $this->createQueryBuilder('q')
            ->andWhere('q.chapter = :chapter_id')
            ->setParameter('chapter_id', $chapter->getId())
            ->orderBy('q.id', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function countOriginalQuestions($conditions)
    {
        $query = $this->createQueryBuilder('q')
            ->select('count(q.id)')
            ->andWhere('q.deletedAt IS NULL');

        if (!empty($conditions['category']))
        {
            $query->leftJoin('q.chapter', 'ch');
            $query->leftJoin('ch.course', 'c');

            $this->setProfessionalCategoriesQueryFilters($query, $conditions);
        }

	    if(!empty($conditions['dateFrom']))
		    $query->andWhere('q.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
	    if(!empty($conditions['dateTo']))
		    $query->andWhere('q.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);

        return $query->getQuery()->getSingleScalarResult();
    }

}
