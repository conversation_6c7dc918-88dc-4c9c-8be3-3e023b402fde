<?php

namespace App\Admin\Traits;

use App\Entity\User;
use App\Service\SettingsService;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Intl\Locales;

trait VueAppDefaultConfiguration
{
    /**
     * @param KeyValueStore $responseParameters
     * @param SettingsService $settings
     * @param AdminContextProvider|RequestStack|Request $context
     * @param JWTManager $JWTManager
     * @param array $pageParams
     * @param array $config
     * @param string $activeRoute
     * @return KeyValueStore
     */
    public function configureAppResponseParameters(
        KeyValueStore $responseParameters,
        SettingsService $settings,
        $context,
        JWTManager $JWTManager,
        array $pageParams = [],
        array $config = [],
        string $activeRoute = 'Home'
    ): KeyValueStore
    {
//        RequestStack $requestStack
        $localeNames = Locales::getNames();
        $locales = [];
        foreach ($settings->get('app.languages') as $locale) {
            $locales[$locale] = $localeNames[$locale];
        }

        /** @var User $user */
        $user = $this->getUser();

        $responseParameters->set('user', [
            'roles' => $user->getRoles(),
            'id' => $user->getId(),
            'firstName' => $user->getFirstName(),
            'lastName' => $user->getLastName()
        ]);

        $responseParameters->set('locales', $locales);
        $responseParameters->set('defaultLocale', $settings->get('app.adminDefaultLanguage'));
        $responseParameters->set('userLocale', $this->getUser()->getLocale());
        $responseParameters->set('froalaKey', $settings->get('app.froalaKey'));

        /**
         * Route for vue use
         */
        if ($context instanceof AdminContextProvider) {
            $request = $context->getContext()->getRequest();
        } elseif ($context instanceof RequestStack) {
            $request = $context->getCurrentRequest();
        } elseif ($context instanceof Request) {
            $request = $context;
        }


        // Always in base64
        $activeParamsB64 = $request->get('activeParams', null);
        // Decode to add other settings
        $activeParams = json_decode(base64_decode($activeParamsB64), true);
        $activeParams = array_merge($activeParams??[], $pageParams);

        $origin = $request->get('origin');
        $id = $request->get('id');
        if (!empty($origin)) $activeParams['origin'] = $origin;
        if (!empty($id)) $activeParams['id'] = $id;

        $responseParameters->set('activeRoute', $request->get('activeRoute', $activeRoute));
        $vueRoute = $request->get('vueRoute', null);
        if (!empty($vueRoute)) {
            $activeParams['vueRoute'] = $vueRoute;
        }
        $responseParameters->set('activeParams', base64_encode(json_encode($activeParams)));

        $appConfig = array_merge($config, [
            'multilingual' => $settings->get('app.multilingual'),
            'filtersEnabled' => $this->settings->get('app.user.useFilters')
        ]);
        // Parameter always in base64
        $responseParameters->set('routeStatus', $request->get('routeStatus'));
        $responseParameters->set('config', base64_encode(json_encode($appConfig)));

        $responseParameters->set('jwt', $this->getJwt($JWTManager, $settings, $user));

        return $responseParameters;
    }

    public function getJwt(JWTManager $JWTManager, SettingsService $settings, User $user): string
    {
        return $JWTManager->createFromPayload(
            $user,
            [
                'roleHierarchy' => $settings->get('security.role_hierarchy.roles'),
                'user' => [
                    'id' => $user->getId(),
                    'firstName' => $user->getFirstName(),
                    'lastName' => $user->getLastName(),
                    'fullName' => $user->getFullName(),
                    'email' => $user->getEmail(),
                    'avatar' => $user->getAvatar(),
                    'locale' => $user->getLocale() ?? $settings->get('app.adminDefaultLanguage')
                ]
            ]
        );
    }
}
