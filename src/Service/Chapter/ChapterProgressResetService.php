<?php

declare(strict_types=1);

namespace App\Service\Chapter;

use App\Entity\Chapter;
use App\Exception\ChapterResetServiceException;
use App\Repository\UserCourseChapterRepository;
use Doctrine\ORM\EntityManagerInterface;

/**
 * Service responsible for resetting SCORM chapter progress
 * This service handles the reset of user progress in SCORM chapters by clearing
 * the SCORM data while preserving essential student information.
 */
class ChapterProgressResetService
{
    private EntityManagerInterface $entityManager;
    private UserCourseChapterRepository $userCourseChapterRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        UserCourseChapterRepository $userCourseChapterRepository
    ) {
        $this->entityManager = $entityManager;
        $this->userCourseChapterRepository = $userCourseChapterRepository;
    }

    /**
     * Resets the current progress of a SCORM chapter
     * This method will clear all SCORM progress data while maintaining student identification.
     *
     * @param Chapter $chapter The chapter whose progress will be reset
     *
     * @throws ChapterResetServiceException If the chapter is not of type SCORM
     */
    public function resetCurrentProgress(Chapter $chapter): void
    {
        if (!$this->isScormChapter($chapter)) {
            throw new ChapterResetServiceException('Only SCORM type chapters can be reset');
        }

        $userCourseChapters = $this->userCourseChapterRepository->findBy(['chapter' => $chapter]);

        foreach ($userCourseChapters as $userCourseChapter) {
            $data = $userCourseChapter->getData();

            if (empty($data)) {
                continue;
            }

            $decodedData = \is_array($data) ? $data : json_decode($data, true);

            if (isset($decodedData['scorm'])) {
                $this->resetScormData($decodedData);
                $userCourseChapter->setData($decodedData);
            } else {
                $userCourseChapter->setData([]);
            }

            $userCourseChapter->setFinishedAt(null);
            $userCourseChapter->setPoints(null);
            $userCourseChapter->setTimeSpent(null);
        }

        $this->entityManager->flush();
    }

    /**
     * Resets SCORM data while preserving student identification.
     *
     * Note: The reset body can be customized by modifying this method.
     * You can define which specific SCORM data fields should be preserved or reset
     * by adding or removing fields from the $data['scorm'] array.
     *
     * @param array &$data Reference to the data array containing SCORM information
     */
    private function resetScormData(array &$data): void
    {
        if (!isset($data['scorm'])) {
            return;
        }

        $studentId = $data['scorm']['cmi.core.student_id'] ?? null;
        $studentName = $data['scorm']['cmi.core.student_name'] ?? null;

        $data['scorm'] = [];

        if (null !== $studentId) {
            $data['scorm']['cmi.core.student_id'] = $studentId;
        }

        if (null !== $studentName) {
            $data['scorm']['cmi.core.student_name'] = $studentName;
        }
    }

    /**
     * Checks if the given chapter is of type SCORM.
     *
     * @param Chapter $chapter The chapter to check
     *
     * @return bool True if the chapter is of type SCORM, false otherwise
     */
    private function isScormChapter(Chapter $chapter): bool
    {
        $type = $chapter->getType();

        return $type && 'scorm' === strtolower($type->getName());
    }
}
