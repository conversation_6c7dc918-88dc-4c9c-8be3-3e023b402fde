<?php

declare(strict_types=1);

namespace App\Service\StatsUser\Games;

use App\Campus\Service\UserCourseChapter\UserCourseService;
use App\Entity\RouletteWord as EntityRouletteWord;
use App\Entity\UserCourseChapter;
use App\Service\SettingsService;
use App\Service\StatsUser\Games\Traits\ScoreAttempGameTrait;
use Doctrine\ORM\EntityManagerInterface;

class RouletteWord implements GamesStragegyResultInterface
{
    use ScoreAttempGameTrait;

    private $em;
    private $settings;
    private UserCourseService $userCourseService;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        UserCourseService $userCourseService
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->userCourseService = $userCourseService;
    }

    public function getResultGame(UserCourseChapter $userCourseChapter): array
    {
        $attempts = [];
        $rouletteWordRepository = $this->em->getRepository(EntityRouletteWord::class);
        $lastDateInQuestions = null;
        if (!empty($userCourseChapter->getData()['attempts'])) {
            foreach ($userCourseChapter->getData()['attempts'] as $key => $attempt) {
                $questions = [];
                $timeTotalAttempt = 0;

                if (empty($attempt)) {
                    continue;
                }

                foreach ($attempt as $question) {
                    $questionFind = $rouletteWordRepository->find($question['questionId']);
                    $timeInQuestion = $question['time'] ? ceil($question['time']) : 0;
                    $lastDateInQuestions = $question['date'] ?? $userCourseChapter->getUpdatedAt()->format('Y-m-d H:i:s');
                    if (!$questionFind) {
                        continue;
                    }

                    $questions[] = [
                        'id' => $questionFind->getId(),
                        'question' => $questionFind->getQuestion(),
                        'correct' => $question['correct'] ?? false,
                        'answers' => $this->getAnswers($questionFind, $question)
                    ];

                    $timeTotalAttempt += $timeInQuestion ?? 0;  // Sumamos el tiempo de la pregunta al tiempo total del intento
                }
                if (!$questions) {
                    continue;
                }

                $attemptForCalculateScore = [
                    'answers' => $attempt ?? [],
                ];

                $attempts[] = [
                    'attempt' => $key + 1,
                    'timeTotal' => ceil($timeTotalAttempt),
                    'state' => $this->getStateGameAttemp($userCourseChapter, $this->userCourseService, $attemptForCalculateScore),
                    'date' => $lastDateInQuestions,
                    'questions' => $questions,
                ];
            }
        }

        return $attempts;
    }

    private function getAnswers(EntityRouletteWord $rouletteWord, $question): array
    {
        $answers = [];

        if (!empty($question['answers'])) {
            return [];
        }
        $answers[] = [
            // 'id' => $rouletteWord->getId(),
            'userAnswer' => $question['value'] ?? null,
            'answer' => $rouletteWord->getWord(),
            'correct' => $question['correct'] ?? false,
            'incorrect' => !$question['correct'] ?? false,
        ];

        return $answers;
    }
}
