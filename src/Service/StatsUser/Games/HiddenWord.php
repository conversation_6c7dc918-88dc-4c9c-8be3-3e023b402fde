<?php

declare(strict_types=1);

namespace App\Service\StatsUser\Games;

use App\Campus\Service\UserCourseChapter\UserCourseService;
use App\Entity\Answer;
use App\Entity\Question;
use App\Entity\UserCourseChapter;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;

class HiddenWord implements GamesStragegyResultInterface
{
    private EntityManagerInterface $em;
    private $settings;
    private UserCourseService $userCourseService;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        UserCourseService $userCourseService
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->userCourseService = $userCourseService;
    }

    public function getResultGame(UserCourseChapter $userCourseChapter): array
    {
        $attempts = [];
        $questionRepository = $this->em->getRepository(Question::class);

        if (!empty($userCourseChapter->getData()['attempts'])) {
            foreach ($userCourseChapter->getData()['attempts'] as $key => $attempt) {
                $timeTotalAttempt = 0;
                $questions = [];

                if (empty($attempt)) {
                    continue;
                }

                foreach ($attempt as $question) {
                    $questionFind = $questionRepository->find($question['questionId']);

                    if (!$questionFind) {
                        continue;
                    }

                    $timeInQuestion = $question['time'] ? ceil($question['time']) : 0;
                    $questions[] = [
                        'id' => $questionFind->getId(),
                        'question' => $questionFind->getQuestion(),
                        'correct' => $question['correct'] ?? false,
                        'answers' => $this->getAnswersQuestion($questionFind, $question),
                    ];

                    $timeTotalAttempt += $timeInQuestion;
                    $attemptForCalculateScore = [
                        'answers' => $attempt,
                        'remainingTime' => $timeTotalAttempt ?? 0,
                    ];
                }

                $lastDateInQuestions = $question['date'] ?? $userCourseChapter->getUpdatedAt()->format('Y-m-d H:i:s');
                $attempts[] = [
                    'attempt' => $key + 1,
                    'timeTotal' => ceil($timeTotalAttempt),
                    'date' => $lastDateInQuestions,
                    'state' => $this->getStateInAttempt($userCourseChapter, $attemptForCalculateScore),
                    'questions' => $questions ?? [],
                ];
            }
        }

        return $attempts;
    }

    private function getAnswersQuestion(Question $question, $answerInTheGame): array
    {
        if (!$question || !$question->getAnswers() || !$answerInTheGame) {
            return [];
        }

        /*$answerUser = $this->getAnswerUser($answerInTheGame['id'] ?? null);
        if (!$answerUser) {
            return [];
        }*/

        $answers = [];

        foreach ($question->getAnswers() as $answer) {
            $correct = $answerInTheGame['correct'] ?? null;
            $answers[] = [
                'answer' => $answer->getAnswer(),
                'userAnswer' => $correct ? $answer->getAnswer() : $answerInTheGame['value'] ?? null,
                'correct' => $correct,
                'incorrect' => !$answerInTheGame['correct'] ?? null,
                'lettersIncorrect' => $answerInTheGame['incorrects'] ?? null,
                'lettersCorrect' => $answerInTheGame['corrects'] ?? null,
            ];
        }

        return $answers;
    }

    public function getAnswerUser($idAnswer): array
    {
        if (!$idAnswer) {
            return [];
        }
        $answer = $this->em->getRepository(Answer::class)->find($idAnswer);

        if (!$answer) {
            return [];
        }

        return [
            'id' => $answer->getId(),
            'answer' => $answer->getAnswer(),
            'correct' => $answer->getCorrect(),
        ];
    }

    private function getStateInAttempt(UserCourseChapter $userCourseChapter, $attempt): string
    {
        $score = $this->userCourseService->getPointByAttempInGame($userCourseChapter, $attempt);

        if ($score > 0) {
            return 'ok';
        }

        return 'ko';
    }
}
