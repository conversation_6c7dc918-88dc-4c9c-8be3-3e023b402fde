<?php

declare(strict_types=1);

namespace App\Service\StatsUser\Games;

use App\Campus\Service\UserCourseChapter\UserCourseService;
use App\Entity\AnswersVideoQuiz;
use App\Entity\UserCourseChapter;
use App\Entity\Videopreguntas;
use App\Entity\Videoquiz as EntityVideoquiz;
use App\Service\SettingsService;
use App\Service\StatsUser\Games\Traits\ScoreAttempGameTrait;
use Doctrine\ORM\EntityManagerInterface;

class VideoQuiz implements GamesStragegyResultInterface
{
    use ScoreAttempGameTrait;
    private $em;
    private $settings;
    private UserCourseService $userCourseService;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        UserCourseService $userCourseService
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->userCourseService = $userCourseService;
    }

    public function getResultGame(UserCourseChapter $userCourseChapter): array
    {
        $attempts = [];
        $lastDateInQuestions = null;
        if (!empty($userCourseChapter->getData()['attempts'])) {
            foreach ($userCourseChapter->getData()['attempts'] as $key => $attempt) {
                $questions = [];
                $timeTotalAttempt = 0;

                if (empty($attempt)) {
                    continue;
                }

                foreach ($attempt as $at) {
                    $questionVideoQuiz = $this->em->getRepository(Videopreguntas::class)->find($at['questionId']);
                    if (!$questionVideoQuiz) {
                        continue;
                    }
                    $lastDateInQuestions = $at['date'] ?? $userCourseChapter->getUpdatedAt()->format('Y-m-d H:i:s');
                    $answerUserVideoQuiz = \array_key_exists('id', $at) ? $this->em->getRepository(AnswersVideoQuiz::class)->find($at['id']) : null;
                    $timeInQuestion = $at['time'] ? ceil($at['time']) : 0;

                    $questions[] = [
                        'id' => $questionVideoQuiz->getId(),
                        'question' => $questionVideoQuiz->getTexto(),
                        'correct' => $at['correct'] ?? false,
                        'answers' => $this->getAnswersVideoQuestion($questionVideoQuiz->getId(), $answerUserVideoQuiz),
                    ];

                    $timeTotalAttempt += $timeInQuestion ?? 0;
                }

                if (!$questions) {
                    continue;
                }

                $attemptForCalculateScore = array_merge(['answers' => $attempt], $this->getQuestionAndTimeVideoQuiz($userCourseChapter));
                $attempts[] = [
                    'attempt' => $key + 1,
                    'timeTotal' => ceil($timeTotalAttempt),  // Agregamos el tiempo total del intento al array de resultados
                    'date' => $lastDateInQuestions,
                    'state' => $this->getStateGameAttemp($userCourseChapter, $this->userCourseService, $attemptForCalculateScore),
                    'questions' => $questions,
                ];
            }
        }

        return $attempts;
    }

    private function getAnswersVideoQuestion($questionId, $answerUserVideoQuiz = null): array
    {
        if (\is_null($questionId)) {
            return [];
        }

        $answerVideoQuiz = $this->em->getRepository(AnswersVideoQuiz::class)->findOneBy(['question' => $questionId, 'isCorrect' => true]);
        $userAnswer = $answerUserVideoQuiz ? $answerUserVideoQuiz->getAnswer() : null;
        $answer = $answerVideoQuiz->getAnswer();
        $correct = $answer == $userAnswer;
        $incorrect = !$correct;

        if (\is_null($userAnswer)) {
            $correct = false;
            $incorrect = false;
        }

        $answers[] = [
            'userAnswer' => $userAnswer,
            'answer' => $answer,
            'correct' => $correct,
            'incorrect' => $incorrect,
        ];

        return $answers;
    }

    private function getQuestionAndTimeVideoQuiz(UserCourseChapter $userCourseChapter): array
    {
        $videoQuiz = $this->em->getRepository(EntityVideoquiz::class)->findOneBy(['chapter' => $userCourseChapter->getChapter()->getId()]);

        $videoQuestions = count($videoQuiz->getVideopreguntas()) ?? 0;

        return [
            'totalQuestions' => $videoQuestions,
            'timeTotal' => $videoQuiz->getVideoDuration(),
        ];
    }
}
