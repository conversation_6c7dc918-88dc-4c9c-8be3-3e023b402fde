<?php

declare(strict_types=1);

namespace App\Service\StatsUser\Games;

use App\Campus\Service\UserCourseChapter\UserCourseService;
use App\Entity\Fillgaps as EntityFillgaps;
use App\Entity\Holes;
use App\Entity\UserCourseChapter;
use App\Service\SettingsService;
use App\Service\StatsUser\Games\Traits\ScoreAttempGameTrait;
use Doctrine\ORM\EntityManagerInterface;

class Fillgaps implements GamesStragegyResultInterface
{
    use ScoreAttempGameTrait;

    private $em;
    private $settings;
    private UserCourseService $userCourseService;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        UserCourseService $userCourseService
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->userCourseService = $userCourseService;
    }

    public function getResultGame(UserCourseChapter $userCourseChapter): array
    {
        $attempts = [];
        $fillGapsRepository = $this->em->getRepository(EntityFillgaps::class);

        if (!empty($userCourseChapter->getData()['attempts'])) {
            foreach ($userCourseChapter->getData()['attempts'] as $key => $attempt) {
                $questions = [];
                $timeTotalAttempt = 0;
                $lastDateInQuestions = null;

                if (empty($attempt)) {
                    continue;
                }

                foreach ($attempt as $question) {
                    $questionFind = $fillGapsRepository->find($question['questionId']);
                    if (!$questionFind) {
                        continue;
                    }
                    $timeInQuestion = $question['time'] ? ceil($question['time']) : 0;
                    $questions[] = [
                        'id' => $questionFind->getId(),
                        'question' => $questionFind->getText(),
                        'correct' => $question['correct'] ?? false,
                        'answers' => $questionFind ? $this->getAnswers($question['attempts'], $questionFind) : [],
                    ];
                    $lastDateInQuestions = $question['date'] ?? $userCourseChapter->getUpdatedAt()->format('Y-m-d H:i:s');
                    $timeTotalAttempt += $timeInQuestion ?? 0;
                    $attemptForCalculateScore = [
                        'answers' => $attempt,
                        'timeTotal' => $questionFind->getTime() ?? 0,
                        'holes' => count($questionFind->getHoles()) ?? 0,
                    ];
                }
                if (!$questions) {
                    continue;
                }

                $attempts[] = [
                    'attempt' => $key + 1,
                    'timeTotal' => ceil($timeTotalAttempt),
                    'date' => $lastDateInQuestions,
                    'state' => $this->getStateGameAttemp($userCourseChapter, $this->userCourseService, $attemptForCalculateScore),
                    'questions' => $questions,
                ];
            }
        }

        return $attempts;
    }

    private function getAnswers($attempts, EntityFillgaps $fillgaps): array
    {
        $answers = [];

        if (!empty($attempts)) {
            foreach ($attempts as $attempt) {
                $fillGapsHole = $this->em->getRepository(Holes::class)->findOneBy(['fillgap' => $fillgaps, 'hole' => $attempt['id']]);
                if ($fillGapsHole) {
                    $answers[] = [
                        'answer' => $this->getCompleteAnswer($fillgaps),
                        'userAnswer' => $fillGapsHole->getAnswer(),
                        'correct' => '1' == $attempt['correct'],
                        'incorrect' => $attempt['incorrect'] ?? false,
                    ];
                }
            }
        } else {
            $answers[] = [
                'answer' => $this->getCompleteAnswer($fillgaps),
                'userAnswer' => '',
                'correct' => false,
                'incorrect' => true,
            ];
        }

        return $answers;
    }

    private function getAnswersGame(EntityFillgaps $fillgaps, $attempt): array
    {
        $answers = [];
        if (!$fillgaps->getHoles()) {
            return [];
        }

        $answersUser = $this->getAnswers($attempt, $fillgaps);

        foreach ($fillgaps->getHoles() as $hole) {
            $answers[] = [
                'id' => $hole->getId(),
                'answer' => $hole->getAnswer(),
                'position' => $hole->getHole(),
            ];
        }

        foreach ($answers as $key => $answer) {
            $answers[$key]['correct'] = false;
            $answers[$key]['incorrect'] = false;

            foreach ($answersUser as $answerUser) {
                if ($answer['position'] == $answerUser['position']) {
                    $answers[$key]['correct'] = $answerUser['correct'];
                    $answers[$key]['incorrect'] = !$answerUser['correct'];
                }
            }
        }

        return $answers;
    }

    public function getCompleteAnswer($questionFind)
    {
        $text = $questionFind->getText();
        $answers = json_decode($questionFind->getAnswers(), true);
        foreach ($answers as $answer) {
            if (isset($answer['id'], $answer['text'])) {
                $placeholders = [
                    '[id' . $answer['id'] . '-select]',
                    '[id' . $answer['id'] . '-drag]',
                ];
                foreach ($placeholders as $placeholder) {
                    $text = str_replace($placeholder, $answer['text'], $text);
                }
            }
        }

        return $text;
    }
}
