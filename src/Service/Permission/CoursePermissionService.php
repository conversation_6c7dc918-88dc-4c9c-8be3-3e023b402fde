<?php

declare(strict_types=1);

namespace App\Service\Permission;

use App\Entity\Course;
use App\Entity\User;
use App\Repository\CourseRepository;

readonly class CoursePermissionService
{
    public function __construct(
        private CourseRepository $courseRepository
    ) {}

    public function canCreate(User $user): bool
    {
        return $this->hasAnyRole($user, [
            User::ROLE_SUPER_ADMIN,
            User::ROLE_ADMIN,
            User::ROLE_CREATOR,
        ]);
    }

    public function canEdit(User $user, Course $course): bool
    {
        if ($this->hasAnyRole($user, [User::ROLE_SUPER_ADMIN, User::ROLE_ADMIN, User::ROLE_MANAGER])) {
            return true;
        }

        return $this->hasRole($user, User::ROLE_CREATOR)
            && $this->isCreatorOrSharedWith($user, $course);
    }

    public function canEditSeason(User $user, Course $course): bool
    {
        if ($this->hasAnyRole($user, [User::ROLE_SUPER_ADMIN, User::ROLE_ADMIN])) {
            return true;
        }

        return $this->hasRole($user, User::ROLE_CREATOR)
            && $this->isCreatorOrSharedWith($user, $course);
    }

    public function canDelete(User $user, Course $course): bool
    {
        return $this->canEdit($user, $course);
    }

    private function isCreatorOrSharedWith(User $user, Course $course): bool
    {
        $query = $this->courseRepository->createQueryBuilder('c')
            ->where('c.id = :courseId')
            ->andWhere('c.createdBy = :userId OR :user MEMBER OF c.managers')
            ->setParameter('courseId', $course->getId())
            ->setParameter('userId', $user->getId())
            ->setParameter('user', $user)
            ->getQuery();

        return (bool) $query->getOneOrNullResult();
    }

    private function hasRole(User $user, string $role): bool
    {
        return in_array($role, $user->getRoles(), true);
    }

    private function hasAnyRole(User $user, array $roles): bool
    {
        return array_intersect($user->getRoles(), $roles) !== [];
    }
}
