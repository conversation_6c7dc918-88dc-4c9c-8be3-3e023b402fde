<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Report;

use App\Service\Annoucement\Report\Generators\AssistanceReportGenerator;
use App\Service\Annoucement\Report\Generators\ConnectionsReportGenerator;
use App\Service\Annoucement\Report\Generators\GroupInfoReportGenerator;
use App\Service\Annoucement\Report\Generators\SurveyReportGenerator;
use App\Service\Annoucement\Report\Generators\IndividualResumeGenerator;
use App\Service\Diploma\DiplomaService;
use App\Utils\SpreadsheetUtil;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\KernelInterface;

class ReportOnSiteService extends AbstractReport
{
    private GroupInfoReportGenerator $groupInfoReportGenerator;
    private ConnectionsReportGenerator $connectionsReportGenerator;
    private SurveyReportGenerator $surveyReportGenerator;
    private AssistanceReportGenerator $assistanceReportGenerator;
    private IndividualResumeGenerator $individualResumeGenerator;
    public const IS_ONLINE_REPORT = false;

    public function __construct(
        EntityManagerInterface $em,
        KernelInterface $kernel,
        AnnouncementReportDataService $announcementReportDataService,
        DiplomaService $diplomaUserService,
        AnnouncementReportConstants $announcementReportConstants,
        LoggerInterface $logger,
        GroupInfoReportGenerator $groupInfoReportGenerator,
        ConnectionsReportGenerator $connectionsReportGenerator,
        SurveyReportGenerator $surveyReportGenerator,
        AssistanceReportGenerator $assistanceReportGenerator,
        IndividualResumeGenerator $individualResumeGenerator
    ) {
        parent::__construct($em, $kernel, $announcementReportDataService, $diplomaUserService, $announcementReportConstants, $logger);

        $this->groupInfoReportGenerator = $groupInfoReportGenerator;
        $this->connectionsReportGenerator = $connectionsReportGenerator;
        $this->surveyReportGenerator = $surveyReportGenerator;
        $this->assistanceReportGenerator = $assistanceReportGenerator;
        $this->individualResumeGenerator = $individualResumeGenerator;
    }

    public function generate(): string
    {
        $initSheetCallback = function (SpreadsheetUtil $report, string $title, array $headers): void {
            $this->initSheet($report, $title, $headers);
        };

        $resumeFiles = '';

        if ($this->confGroupInfo) {
            $this->groupInfoReportGenerator->generate(
                $this->announcementContainer,
                $this->announcementDir,
                $initSheetCallback,
                self::IS_ONLINE_REPORT
            );
        }

        if ($this->confConnections) {
            $this->connectionsReportGenerator->generate(
                $this->announcementContainer,
                $this->announcementDir,
                $initSheetCallback,
                self::IS_ONLINE_REPORT
            );
        }

        if ($this->confAssistance) {
            $this->assistanceReportGenerator->generate(
                $this->announcementContainer,
                $this->announcementDir,
                $initSheetCallback,
                self::IS_ONLINE_REPORT
            );
        }

        if ($this->confSurvey) {
            $this->surveyReportGenerator->generate(
                $this->announcementContainer,
                $this->announcementDir,
                $initSheetCallback,
                self::IS_ONLINE_REPORT
            );
        }

        if ($this->confResumeIndividual) {
            $resumeFiles = $this->individualResumeGenerator->generate(
                $this->announcementContainer,
                $this->announcementDir,
                $this->userId
            );
        }

        return $this->announcementDir . ($resumeFiles != '' ? $resumeFiles : '');
    }
}
