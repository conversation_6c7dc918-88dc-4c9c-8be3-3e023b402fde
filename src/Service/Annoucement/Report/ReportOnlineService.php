<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Report;

use App\Service\Annoucement\Report\Generators\ActivityReportGenerator;
use App\Service\Annoucement\Report\Generators\ChatReportGenerator;
use App\Service\Annoucement\Report\Generators\ConnectionsReportGenerator;
use App\Service\Annoucement\Report\Generators\ForumReportGenerator;
use App\Service\Annoucement\Report\Generators\GroupInfoReportGenerator;
use App\Service\Annoucement\Report\Generators\MaterialReportGenerator;
use App\Service\Annoucement\Report\Generators\NotificationReportGenerator;
use App\Service\Annoucement\Report\Generators\SurveyReportGenerator;
use App\Service\Annoucement\Report\Generators\TaskReportGenerator;
use App\Service\Annoucement\Report\Generators\IndividualResumeGenerator;
use App\Service\Diploma\DiplomaService;
use App\Utils\SpreadsheetUtil;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\KernelInterface;

class ReportOnlineService extends AbstractReport
{
    private GroupInfoReportGenerator $groupInfoReportGenerator;
    private ForumReportGenerator $forumReportGenerator;
    private ChatReportGenerator $chatReportGenerator;
    private ConnectionsReportGenerator $connectionsReportGenerator;
    private ActivityReportGenerator $activityReportGenerator;
    private NotificationReportGenerator $notificationReportGenerator;
    private MaterialReportGenerator $materialReportGenerator;
    private TaskReportGenerator $taskReportGenerator;
    private SurveyReportGenerator $surveyReportGenerator;

    private IndividualResumeGenerator $individualResumeGenerator;
    public const IS_ONLINE_REPORT = true;

    public function __construct(
        EntityManagerInterface $em,
        KernelInterface $kernel,
        AnnouncementReportDataService $announcementReportDataService,
        DiplomaService $diplomaUserService,
        AnnouncementReportConstants $announcementReportConstants,
        LoggerInterface $logger,
        GroupInfoReportGenerator $groupInfoReportGenerator,
        ForumReportGenerator $forumReportGenerator,
        ChatReportGenerator $chatReportGenerator,
        ConnectionsReportGenerator $connectionsReportGenerator,
        ActivityReportGenerator $activityReportGenerator,
        NotificationReportGenerator $notificationReportGenerator,
        MaterialReportGenerator $materialReportGenerator,
        TaskReportGenerator $taskReportGenerator,
        SurveyReportGenerator $surveyReportGenerator,
        IndividualResumeGenerator $individualResumeGenerator
    ) {
        parent::__construct($em, $kernel, $announcementReportDataService, $diplomaUserService, $announcementReportConstants, $logger);

        $this->groupInfoReportGenerator = $groupInfoReportGenerator;
        $this->forumReportGenerator = $forumReportGenerator;
        $this->chatReportGenerator = $chatReportGenerator;
        $this->connectionsReportGenerator = $connectionsReportGenerator;
        $this->activityReportGenerator = $activityReportGenerator;
        $this->notificationReportGenerator = $notificationReportGenerator;
        $this->materialReportGenerator = $materialReportGenerator;
        $this->taskReportGenerator = $taskReportGenerator;
        $this->surveyReportGenerator = $surveyReportGenerator;
        $this->individualResumeGenerator = $individualResumeGenerator;
    }


    public function generate(): string
    {
        $initSheetCallback = function (SpreadsheetUtil $report, string $title, array $headers): void {
            $this->initSheet($report, $title, $headers);
        };
        $resumeFiles = '';

        if ($this->confGroupInfo) {
            $this->groupInfoReportGenerator->generate(
                $this->announcementContainer,
                $this->announcementDir,
                $initSheetCallback,
                self::IS_ONLINE_REPORT
            );
        }

        if ($this->confForum) {
            $this->forumReportGenerator->generate(
                $this->announcementContainer,
                $this->announcementDir,
                $initSheetCallback,
                self::IS_ONLINE_REPORT
            );
        }

        if ($this->confChat) {
            $this->chatReportGenerator->generate(
                $this->announcementContainer,
                $this->announcementDir,
                $initSheetCallback,
                self::IS_ONLINE_REPORT
            );
        }

        if ($this->confConnections) {
            $this->connectionsReportGenerator->generate(
                $this->announcementContainer,
                $this->announcementDir,
                $initSheetCallback,
                self::IS_ONLINE_REPORT
            );
        }

        if ($this->confActivities) {
            $this->activityReportGenerator->generate(
                $this->announcementContainer,
                $this->announcementDir,
                $initSheetCallback,
                self::IS_ONLINE_REPORT
            );
        }

        if ($this->confNotifications) {
            $this->notificationReportGenerator->generate(
                $this->announcementContainer,
                $this->announcementDir,
                $initSheetCallback,
                self::IS_ONLINE_REPORT
            );
        }

        if ($this->confMaterials) {
            $this->materialReportGenerator->generate(
                $this->announcementContainer,
                $this->announcementDir,
                $initSheetCallback,
                self::IS_ONLINE_REPORT
            );
        }

        if ($this->confTasks) {
            $this->taskReportGenerator->generate(
                $this->announcementContainer,
                $this->announcementDir,
                $initSheetCallback,
                self::IS_ONLINE_REPORT
            );
        }

        if ($this->confSurvey) {
            $this->surveyReportGenerator->generate(
                $this->announcementContainer,
                $this->announcementDir,
                $initSheetCallback,
                self::IS_ONLINE_REPORT
            );
        }

        if ($this->confResumeIndividual) {
            $resumeFiles = $this->individualResumeGenerator->generate(
                $this->announcementContainer,
                $this->announcementDir,
                $this->userId
            );
        }

        return $this->announcementDir . ($resumeFiles != '' ? $resumeFiles : '');
    }
}
