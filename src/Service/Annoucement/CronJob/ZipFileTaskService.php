<?php

declare(strict_types=1);

namespace App\Service\Annoucement\CronJob;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\Course;
use App\Entity\User;
use App\Entity\ZipFileTask;
use App\Enum\ZipFileTaskEnum;
use App\Exceptions\ZipFileTaskException;
use App\Service\Annoucement\Report\AnnouncementReportService;
use App\Service\Course\Report\General\ContextStatsReports as CourseContextStatsReports;
use App\Service\Course\Report\General\CourseStatsReport;
use App\Service\Course\Report\Persons\ItineraryReport;
use App\Service\Course\Report\Persons\PersonStatsReport;
use App\Service\Diploma\DiplomaService;
use App\Service\Itinerary\General\ItineraryCourseReports;
use App\Service\Nps\ContextStatsReports as NpsContextStatsReports;
use App\Service\Nps\ExcelOpinionsReportService;
use App\Service\SettingsService;
use App\Service\TemplatedEmail\TemplatedEmailService;
use App\Utils\ZipFile;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * All methods must return the list of directories to be processed.
 */
class ZipFileTaskService
{
    /** @var string path relative to app.file_manager.base_dir */
    public const ZIP_PATH = 'compressed';

    private EntityManagerInterface $em;
    private AnnouncementReportService $announcementReportService;
    private ItineraryReport $itineraryReport;

    private ItineraryCourseReports $itineraryCourseReports;
    private CourseStatsReport $courseStatsReport;

    private PersonStatsReport $personStatsReport;

    private LoggerInterface $logger;
    private string $zipFilesDirPath;

    private TranslatorInterface $translator;

    private SettingsService $settings;

    private DiplomaService $diplomaService;
    private TemplatedEmailService $templatedEmailService;

    private ExcelOpinionsReportService $excelOpinionsReportService;

    public function __construct(
        EntityManagerInterface $em,
        LoggerInterface $logger,
        TranslatorInterface $translator,
        AnnouncementReportService $announcementReportService,
        ItineraryReport $itineraryReport,
        CourseStatsReport $courseStatsReport,
        PersonStatsReport $personStatsReport,
        ParameterBagInterface $params,
        ItineraryCourseReports $itineraryCourseReports,
        SettingsService $settings,
        DiplomaService $diplomaService,
        ExcelOpinionsReportService $excelOpinionsReportService,
        TemplatedEmailService $templatedEmailService
    ) {
        $this->em = $em;
        $this->logger = $logger;
        $this->zipFilesDirPath = ZipFile::checkPath($params->get('app.file_manager.base_dir'), ZipFileTaskEnum::ZIP_PATH_COMPRESSED);
        if (!file_exists($this->zipFilesDirPath)) {
            mkdir($this->zipFilesDirPath);
        }
        $this->translator = $translator;
        $this->announcementReportService = $announcementReportService;
        $this->itineraryReport = $itineraryReport;
        $this->courseStatsReport = $courseStatsReport;
        $this->personStatsReport = $personStatsReport;
        $this->itineraryCourseReports = $itineraryCourseReports;
        $this->settings = $settings;
        $this->diplomaService = $diplomaService;
        $this->excelOpinionsReportService = $excelOpinionsReportService;
        $this->templatedEmailService = $templatedEmailService;
    }

    /**
     * @throws \Throwable
     */
    public function zip(ZipFileTask $zipFileTask)
    {
        try {
            $zipFileTask->setStartedAt(new \DateTimeImmutable())
                ->setStatus(ZipFileTask::STATUS_IN_PROGRESS);

            $this->em->persist($zipFileTask);
            $this->em->flush();

            $entityId = $zipFileTask->getEntityId() ?: '';
            $zipFileTask->setEntityId($entityId);

            $createdBy = $zipFileTask->getCreatedBy();
            if ($createdBy) {
                $locale = $createdBy->getLocale();
                $this->translator->setLocale($locale);
            }

            /** @var string|array $result */
            $result = $this->{$zipFileTask->getFunction()}($zipFileTask);

            $isNotNormalReport = false;
            $isResume = false;

            if (false !== strpos($result, 'itinerary-report')
                || false !== strpos($result, 'diplomas-report')
                || false !== strpos($result, 'pdf-resume-users')
            ) {
                if (false !== strpos($result, 'diplomas-report') && 15 === \strlen($result)) {
                    throw new ZipFileTaskException(
                        'Failed to generate zip file for task, no data ' . $zipFileTask->getId()
                    );
                }
                if (false !== strpos($result, 'pdf-resume-users')) {
                    $files = [];
                    foreach (explode('&& ', $result) as $key => $value) {
                        if (1 != $key) {
                            $files[] = $value;
                        }
                    }
                    $result = $files;
                    $isResume = true;
                } else {
                    $lengthStringCut = (false !== strpos($result, 'itinerary-report')) ? 16 : 15;
                    $result = explode('&& ', substr($result, $lengthStringCut));
                    $isNotNormalReport = true;
                }
            }

            if (\is_array($result) && \count($result) > 0) {
                $this->generateZipFile($zipFileTask, $result, $isNotNormalReport, $isResume);
            } elseif (\is_string($result) && !empty($result)) {
                $this->generateZipFile($zipFileTask, [$result], $isNotNormalReport, $isResume);
            }

            $zipFileTask->setEntityId($entityId);
            $zipFileTask->setStatus(ZipFileTask::STATUS_COMPLETED);
        } catch (\Throwable $e) {
            $zipFileTask->setStatus(ZipFileTask::STATUS_FAILED);
            $this->em->flush();

            $this->logger->warning('ZIP Task Error: ' . json_encode($e->getMessage()));

            throw $e;
        } finally {
            $zipFileTask->setFinishedAt(new \DateTimeImmutable());

            $availableAt = new \DateTimeImmutable();
            $days = $this->settings->get('app.zip.day_available_until');
            $newAvailableAt = $availableAt->add(new \DateInterval('P' . $days . 'D'));
            $zipFileTask->setAvailableAt($newAvailableAt);

            $this->em->flush();
        }
    }

    /**
     * @throws \Exception
     */
    public function announcementReport(ZipFileTask $zipFileTask)
    {
        //        $params = $zipFileTask->getParams()['params'];
        $entityId = $zipFileTask->getEntityId();
        if (empty($entityId)) {
            throw new \Exception('Announcement zip requires an entity id');
        }
        $announcement = null;
        $announcementGroup = null;
        $createdBy = $zipFileTask->getCreatedBy();
        if (AnnouncementReportService::TYPE_GROUP === $zipFileTask->getType()) {
            $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->find($entityId);
            if (!$announcementGroup) {
                throw new \Exception('Announcement Group not found');
            }
            $announcement = $announcementGroup->getAnnouncement();
        } elseif (AnnouncementReportService::TYPE_ANNOUNCEMENT === $zipFileTask->getType()) {
            $announcement = $this->em->getRepository(Announcement::class)->find($entityId);
        } else {
            throw new ZipFileTaskException('Type not supported when calling announcementZip');
        }

        if (!$announcement) {
            throw new \Exception('Announcement not found');
        }

        return $this->announcementReportService->generateReport(
            $announcement,
            $createdBy,
            null == $announcementGroup,
            $announcementGroup ? [$announcementGroup] : [],
            $zipFileTask->getParams()
        );
    }

    public function generateZipFile(ZipFileTask $task, array $directoriesOrFiles = [], $isNotNormalReport = false, $isResume = false)
    {
        $filename = bin2hex(random_bytes(10)) . (new \DateTime())->getTimestamp();

        $zipFile = new ZipFile();
        $zipFile->setFilename($filename)
            ->setFiles($directoriesOrFiles)
            ->setPath($this->zipFilesDirPath);

        $userName = null;
        if ($isNotNormalReport && !empty($task->getParams()['params']['userId'])) {
            $user = $this->em->getRepository(User::class)->find($task->getParams()['params']['userId']);
            $userName = $user->getFullName();
        }

        if (true !== $zipFile->generate($isNotNormalReport, $isResume, $userName)) {
            throw new ZipFileTaskException('Failed to generate zip file for task ' . $task->getId());
        }

        $task->setFilename($filename);

        // Generate FilesManager
        $filesManager = $this->em->getRepository(ZipFileTask::class)->generateFilesManagerFile($task);
        $task->setFilesManager($filesManager);
        // Remove src folders to avoid using storage
        try {
            foreach ($directoriesOrFiles as $dirOrFile) {
                if (file_exists($dirOrFile)) {
                    if (is_dir($dirOrFile)) {
                        $this->removeDir($dirOrFile);
                    } else {
                        unlink($dirOrFile);
                    }
                }
            }
        } catch (\Exception $e) {
            $this->logger->error('ZipTask: Failed to delete: ' . $e->getMessage(), $e->getTrace());
        }
    }

    private function removeDir($path): bool
    {
        $dir = opendir($path);
        while (false !== ($file = readdir($dir))) {
            if ('.' != $file && '..' != $file) {
                $full = $path . '/' . $file;
                if (is_dir($full)) {
                    $this->removeDir($full);
                } else {
                    unlink($full);
                }
            }
        }
        closedir($dir);

        return rmdir($path);
    }

    /**
     * Called automatically based on params.
     *
     * @return string Directory where result are saved
     */
    public function course_perCourseStatsReport(ZipFileTask $zipFileTask): string
    {
        $params = $zipFileTask->getParams()['params'] ?? [];
        $params['createdById'] = $zipFileTask->getCreatedBy()->getId();

        if (!empty($params['itineraryId'])) {
            $params['itineraryId'] = $zipFileTask->getEntityId();
            $strategy = new CourseContextStatsReports($this->itineraryReport);

            return $strategy->generateReport(null, $params);
        } else {
            $courseId = $zipFileTask->getEntityId();
            $course = $this->em->getRepository(Course::class)->find($courseId);

            if (!$course) {
                throw new ZipFileTaskException('Course not found with ID: ' . $courseId);
            }

            if (!empty($params['userId'])) {
                $strategy = new CourseContextStatsReports($this->personStatsReport);
            } else {
                $strategy = new CourseContextStatsReports($this->courseStatsReport);
            }

            return $strategy->generateReport($course, $params);
        }
    }

    public function itinerary_perItineraryStatsReport(ZipFileTask $zipFileTask): string
    {
        $itineraryId = $zipFileTask->getEntityId();
        $params = $zipFileTask->getParams()['params'] ?? [];
        $params['createdById'] = $zipFileTask->getCreatedBy()->getId();

        return $this->itineraryCourseReports->generate($itineraryId, $params);
    }

    public function diploma_courses_report(ZipFileTask $zipFileTask): string
    {
        $params = $zipFileTask->getParams()['params'] ?? [];
        $params['createdBy'] = $zipFileTask->getCreatedBy()->getId();

        return $this->diplomaService->generateDiplomas($params);
    }

    public function diplomas_user_report(ZipFileTask $zipFileTask): string
    {
        $params = $zipFileTask->getParams()['params'] ?? [];
        $userId = $params['userId'] ?? null;
        $typeOfDiplomas = $params['typeOfDiplomas'] ?? null;

        try {
            $user = $this->em->getRepository(User::class)->find($userId);
            if (!$user) {
                throw new ZipFileTaskException('User not found with ID: ' . $userId);
            }

            $result = $this->diplomaService->generateUserDiplomas($params);
            if (empty($result)) {
                throw new ZipFileTaskException('No diplomas found to generate');
            }

            return $result;
        } catch (\Exception $e) {
            $typeMsg = '';
            if ('0' == $typeOfDiplomas || 0 === $typeOfDiplomas) {
                $typeMsg = ' (searching by itineraries)';
            } elseif ('1' == $typeOfDiplomas || 1 === $typeOfDiplomas) {
                $typeMsg = ' (searching by filters)';
            } elseif ('2' == $typeOfDiplomas || 2 === $typeOfDiplomas) {
                $typeMsg = ' (searching by announcements)';
            }

            throw new ZipFileTaskException('No diplomas found to generate' . $typeMsg . ' for user ID: ' . $userId);
        }
    }

    public function opinion_report(ZipFileTask $zipFileTask): string
    {
        $params = $zipFileTask->getParams()['params'] ?? [];
        $strategy = new NpsContextStatsReports($this->excelOpinionsReportService);

        return $strategy->generateReport($params);
    }
}
