<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\Entity\User;
use App\V2\Application\Query\Admin\GetUsers;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\User\UserCriteriaTransformer;
use App\V2\Infrastructure\User\UserListItemDTOTransformer;
use App\V2\Infrastructure\Validator\Admin\GetUsersValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetUsersController extends QueryBusAccessor
{
    /**
     * @throws ValidatorException
     * @throws CollectionException
     */
    public function __invoke(Request $request): Response
    {
        $queryParameters = $request->query->all();

        GetUsersValidator::validateGetUsersRequest($queryParameters);

        $userCriteria = UserCriteriaTransformer::fromArray($queryParameters);

        /** @var User $user */
        $user = $this->getUser();

        $paginatedDTOCollection = $this->ask(
            new GetUsers(
                criteria: $userCriteria,
                requestUser: $user,
            )
        );

        return new JsonResponse(
            ApiResponseContent::createFromData(
                UserListItemDTOTransformer::fromDTOCollectionToArray(
                    $paginatedDTOCollection->getCollection()
                )
            )
                ->addPaginationMetadata($paginatedDTOCollection, $userCriteria)
                ->toArray(),
            status: Response::HTTP_OK,
        );
    }
}
