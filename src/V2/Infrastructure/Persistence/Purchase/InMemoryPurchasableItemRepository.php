<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Purchase;

use App\V2\Domain\Purchase\Exception\PurchasableItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PurchasableItem;
use App\V2\Domain\Purchase\PurchasableItemCollection;
use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Infrastructure\Persistence\InMemoryCommonCriteriaBuilder;

class InMemoryPurchasableItemRepository implements PurchasableItemRepository
{
    private PurchasableItemCollection $items;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->items = new PurchasableItemCollection([]);
    }

    /**
     * @throws CollectionException
     * @throws PurchasableItemRepositoryException
     */
    #[\Override]
    public function put(PurchasableItem $item): void
    {
        try {
            $this->findOneBy(
                PurchasableItemCriteria::createEmpty()
                    ->filterByResource($item->getResource())
            );
            throw PurchasableItemRepositoryException::duplicateResource($item->getResource());
        } catch (PurchasableItemNotFoundException) {
            $items = $this->items->allIndexedById();
            $items[$item->getId()->value()] = $item;
            $this->items->replace($items);
        }
    }

    /**
     * @throws PurchasableItemNotFoundException
     * @throws CollectionException
     */
    #[\Override]
    public function findOneBy(PurchasableItemCriteria $criteria): PurchasableItem
    {
        $purchasableItems = $this->filterByCriteria($criteria);

        if ($purchasableItems->isEmpty()) {
            throw new PurchasableItemNotFoundException();
        }

        return $purchasableItems->first();
    }

    /**
     * @throws CollectionException
     */
    #[\Override]
    public function findBy(PurchasableItemCriteria $criteria): PurchasableItemCollection
    {
        return $this->filterByCriteria($criteria);
    }

    /**
     * @throws CollectionException
     */
    #[\Override]
    public function delete(PurchasableItem $item): void
    {
        $items = $this->items->allIndexedById();
        unset($items[$item->getId()->value()]);
        $this->items->replace($items);
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(PurchasableItemCriteria $criteria): PurchasableItemCollection
    {
        $items = $this->items->filter(
            fn (PurchasableItem $item) => (
                null === $criteria->getSearch()
                    || str_contains($item->getName(), $criteria->getSearch())
            ) && (
                null === $criteria->getResource()
                    || $criteria->getResource()->equals($item->getResource())
            ) && (
                null === $criteria->getMinPrice()
                    || $item->getPrice()->greaterThanOrEqual($criteria->getMinPrice())
            ) && (
                null === $criteria->getMaxPrice()
                    || $item->getPrice()->lessThanOrEqual($criteria->getMaxPrice())
            )
        );

        /** @var PurchasableItemCollection $items */
        $items = InMemoryCommonCriteriaBuilder::filterByCommonCriteria($criteria, $items);

        return $items;
    }
}
