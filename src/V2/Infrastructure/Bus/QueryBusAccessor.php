<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Bus;

use App\V2\Domain\Bus\Query;
use App\V2\Domain\Bus\QueryBus;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

abstract class QueryBusAccessor extends AbstractController
{
    public function __construct(private readonly QueryBus $queryBus)
    {
    }

    protected function ask(Query $query): mixed
    {
        return $this->queryBus->ask($query);
    }
}
