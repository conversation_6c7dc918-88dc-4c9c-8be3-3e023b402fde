<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Service;

use App\Controller\Admin\AnnouncementCrudController;
use App\Controller\Admin\ChallengeCrudController;
use App\Controller\Admin\CourseCrudController;
use App\Controller\Admin\CourseLevelCrudController;
use App\Controller\Admin\CourseSegmentCategoryCrudController;
use App\Controller\Admin\CronCrudController;
use App\Controller\Admin\DocumentationCrudController;
use App\Controller\Admin\HelpTextCrudController;
use App\Controller\Admin\ItineraryCrudController;
use App\Controller\Admin\LibraryCrudController;
use App\Controller\Admin\LtiToolCrudController;
use App\Controller\Admin\ManagerCrudController;
use App\Controller\Admin\NewsCrudController;
use App\Controller\Admin\NpsCrudController;
use App\Controller\Admin\SettingCatalogCrudController;
use App\Controller\Admin\UserCrudController;
use App\Controller\SuperAdmin\DeveloperController;
use App\Entity\User;
use App\Modules\CategoryFilter\Controller\CategoryFilterController;
use App\Modules\CourseSection\Controller\SectionCrudController;
use App\Modules\HelpCategory\Controller\HelpCategoryCrudController;
use App\Modules\Pages\Controller\PagesCrudController;
use App\Modules\User\Company\Controller\UserCompanyCrudController;
use App\Service\SettingsService;
use App\V2\Infrastructure\Security\FirewallException;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;

readonly class EasyAdminRouteConverter
{
    private array $dashboard;

    public function __construct(
        private AdminUrlGenerator $adminUrlGenerator,
        private AuthorizationCheckerInterface $authorizationChecker,
        SettingsService $settings,
    ) {
        $this->dashboard = [
            'categories' => [
                'role' => User::ROLE_ADMIN,
                'route' => 'admin-course-categories-page',
            ],
            'nps' => [
                'role' => User::ROLE_MANAGER,
                'controller' => NpsCrudController::class,
            ],
            'courses' => [
                'role' => [User::ROLE_CREATOR, User::ROLE_MANAGER],
                'controller' => CourseCrudController::class,
            ],
            'course-level' => [
                'role' => User::ROLE_ADMIN,
                'controller' => CourseLevelCrudController::class,
                'enabled' => $settings->get('app.setCourseLevel'),
            ],
            'itinerary' => [
                'role' => User::ROLE_MANAGER,
                'controller' => ItineraryCrudController::class,
                'enabled' => $settings->get('app.use_itinerary'),
            ],
            'survey' => [
                'role' => User::ROLE_ADMIN,
                'route' => 'admin-app-survey',
                'enabled' => $settings->get('app.course.survey'),
            ],
            'announcements' => [
                'role' => [User::ROLE_MANAGER, User::ROLE_TUTOR, User::ROLE_CREATOR],
                'controller' => AnnouncementCrudController::class,
                'enabled' => $settings->get('app.announcement.enabled'),
            ],
            'challenges' => [
                'role' => User::ROLE_ADMIN,
                'controller' => ChallengeCrudController::class,
                'enabled' => $settings->get('app.challenge.enabled'),
            ],
            'users' => [
                'role' => User::ROLE_MANAGER,
                'controller' => UserCrudController::class,
            ],
            'users-managers' => [
                'role' => User::ROLE_ADMIN,
                'controller' => ManagerCrudController::class,
            ],
            'filters' => [
                'role' => User::ROLE_ADMIN,
                'controller' => CategoryFilterController::class,
                'enabled' => $settings->get('app.user.useFilters'),
            ],
            'help' => [
                'role' => User::ROLE_ADMIN,
                'controller' => HelpTextCrudController::class,
            ],
            'help-category' => [
                'role' => User::ROLE_ADMIN,
                'controller' => HelpCategoryCrudController::class,
            ],
            'news' => [
                'role' => User::ROLE_ADMIN,
                'controller' => NewsCrudController::class,
                'enabled' => $settings->get('app.news.enabled'),
            ],
            'stats' => [
                'role' => [User::ROLE_ADMIN, User::ROLE_MANAGER, User::ROLE_MANAGER_EDITOR],
                'route' => 'admin_stats',
            ],
            'stats-export' => [
                'role' => [User::ROLE_ADMIN, User::ROLE_MANAGER, User::ROLE_MANAGER_EDITOR],
                'route' => 'admin_stats_export',
            ],
            'zip-files' => [
                'role' => [User::ROLE_ADMIN, User::ROLE_MANAGER, User::ROLE_MANAGER_EDITOR],
                'route' => 'admin_zip_files',
            ],
            'stats-accumulative' => [
                'role' => User::ROLE_ADMIN,
                'route' => 'stats-accumulative',
                'enabled' => $settings->get('app.stats.accumulative'),
            ],
            'stats-segmented' => [
                'role' => User::ROLE_ADMIN,
                'route' => 'stats-segmented',
                'enabled' => $settings->get('app.stats.accumulative'),
            ],
            'documentation' => [
                'role' => User::ROLE_MANAGER,
                'controller' => DocumentationCrudController::class,
                'enabled' => $settings->get('app.documentation.enabled'),
            ],
            'pages' => [
                'role' => User::ROLE_MANAGER,
                'controller' => PagesCrudController::class,
                'enabled' => $settings->get('app.pages'),
            ],
            'user-company' => [
                'role' => User::ROLE_MANAGER,
                'controller' => UserCompanyCrudController::class,
                'enabled' => $settings->get('app.user_company.enabled'),
            ],
            'library' => [
                'role' => User::ROLE_MANAGER,
                'controller' => LibraryCrudController::class,
                'enabled' => $settings->get('app.library.enabled'),
            ],
            'segments' => [
                'role' => User::ROLE_SUPER_ADMIN,
                'controller' => CourseSegmentCategoryCrudController::class,
                'enabled' => $settings->get('app.useSegment'),
            ],
            'filter-category' => [
                'role' => User::ROLE_SUPER_ADMIN,
                'controller' => CategoryFilterController::class,
                'enabled' => $settings->get('app.user.useFilters'),
            ],
            'menu-general' => [
                'role' => User::ROLE_SUPER_ADMIN,
                'route' => 'admin_settings',
            ],
            'catalog' => [
                'role' => User::ROLE_SUPER_ADMIN,
                'controller' => SettingCatalogCrudController::class,
            ],
            'sections' => [
                'role' => User::ROLE_SUPER_ADMIN,
                'controller' => SectionCrudController::class,
            ],
            'developer' => [
                'role' => User::ROLE_SUPER_ADMIN,
                'controller' => DeveloperController::class,
            ],
            'saml' => [
                'role' => User::ROLE_SUPER_ADMIN,
                'route' => 'admin_saml_settings',
            ],
            'integrations' => [
                'role' => User::ROLE_SUPER_ADMIN,
                'route' => 'integrations-app-index',
            ],
            'import-users' => [
                'role' => User::ROLE_SUPER_ADMIN,
                'route' => 'admin_users_import',
            ],
            'cron' => [
                'role' => User::ROLE_SUPER_ADMIN,
                'controller' => CronCrudController::class,
            ],
            'php-info' => [
                'role' => User::ROLE_SUPER_ADMIN,
                'route' => 'admin_phpinfo',
            ],
            'lti-tool-legacy' => [
                'role' => User::ROLE_SUPER_ADMIN,
                'controller' => LtiToolCrudController::class,
            ],
            'to-front' => [
                'role' => User::ROLE_USER,
                'route' => 'goToFront',
            ],
        ];
    }

    public function getEasyAdminUrl(Request $request): ?Response
    {
        $route = $request->get('ea-route');
        $action = $request->get('ea-action', 'index');
        $entityId = $request->get('ea-entity-id');

        $route = $this->dashboard[$route] ?? null;
        if (null === $route) {
            return null;
        }

        if (!($route['enabled'] ?? true)) {
            return null;
        }

        $role = $route['role'];
        $granted = false;
        if (\is_array($role)) {
            foreach ($role as $r) {
                if ($this->authorizationChecker->isGranted($r)) {
                    $granted = true;
                    break;
                }
            }
        } else {
            $granted = $this->authorizationChecker->isGranted($role);
        }

        if (false === $granted) {
            throw FirewallException::forbidden();
        }

        $ea = $this->adminUrlGenerator->unsetAll();

        $controller = $route['controller'] ?? null;
        if ($controller) {
            $ea->setController($controller)
                ->setAction($action);

            if ('index' !== $action && null !== $entityId) {
                $ea->setEntityId($entityId);
            }
        } else {
            $ea->setRoute($route['route']);
        }

        return new RedirectResponse(
            $ea->addSignature()
                ->generateUrl()
        );
    }
}
