<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Log;

use App\V2\Application\Log\Logger;
use Monolog\Handler\StreamHandler;

class MonologLogger implements Logger
{
    private \Monolog\Logger $logger;
    private const string LOG_FILENAME = 'app.log';

    public function __construct(private readonly string $logPath)
    {
        $this->logger = new \Monolog\Logger('app');
        $this->logger->pushHandler(new StreamHandler($this->getFileName(), \Monolog\Logger::ERROR));
    }

    private function getFileName(): string
    {
        return $this->logPath . DIRECTORY_SEPARATOR . self::LOG_FILENAME;
    }

    public function error(string $message, ?\Throwable $exception = null, array $extraData = []): void
    {
        $this->logger->error($message, array_merge(
            $exception ? ['exception' => $exception] : [],
            $extraData,
        ));
    }

    public function getLogMessages(): array
    {
        if (!file_exists($this->getFileName())) {
            return [];
        }

        $logContent = file_get_contents($this->getFileName());

        return explode(PHP_EOL, $logContent);
    }
}
