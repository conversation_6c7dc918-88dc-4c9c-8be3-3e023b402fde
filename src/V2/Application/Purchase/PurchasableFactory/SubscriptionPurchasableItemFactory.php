<?php

declare(strict_types=1);

namespace App\V2\Application\Purchase\PurchasableFactory;

use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\PurchasableItem;
use App\V2\Domain\Purchase\PurchasableItemFactory;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Shared\Uuid\UuidGenerator;
use App\V2\Domain\Subscription\Subscription;

readonly class SubscriptionPurchasableItemFactory implements PurchasableItemFactory
{
    public function __construct(private UuidGenerator $uuidGenerator)
    {
    }

    public function supports(ResourceType $type): bool
    {
        return ResourceType::Subscription === $type;
    }

    /**
     * @throws InvalidPurchasableItemException
     */
    public function create(mixed $source, Money $price): PurchasableItem
    {
        if (!$source instanceof Subscription) {
            throw new \InvalidArgumentException('Expected Subscription');
        }

        return new PurchasableItem(
            id: $this->uuidGenerator->generate(),
            name: $source->getName(),
            description: $source->getDescription() ?? '',
            price: $price,
            resource: new Resource(
                type: ResourceType::Subscription,
                id: $source->getId()
            ),
            createdAt: new \DateTimeImmutable(),
        );
    }
}
