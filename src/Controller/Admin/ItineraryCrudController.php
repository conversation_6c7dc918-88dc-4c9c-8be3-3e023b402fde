<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Admin\Field\FosCkeditorField;
use App\Admin\Filter\ItineraryCourseFilter;
use App\Admin\Filter\ItineraryCreatedByFilter;
use App\Admin\Traits\FilterCategoriesTrait;
use App\Admin\Traits\FiltersClearTrait;
use App\Admin\Traits\SerializerTrait;
use App\Admin\Traits\VueAppDefaultConfiguration;
use App\Entity\Course;
use App\Entity\Department;
use App\Entity\EmailNotification;
use App\Entity\Export;
use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\FilterCategoryTranslation;
use App\Entity\FilterTranslation;
use App\Entity\Itinerary;
use App\Entity\ItineraryCourse;
use App\Entity\ItineraryManager;
use App\Entity\ItineraryTranslation;
use App\Entity\ItineraryUser;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Enum\StatsReportType;
use App\Modules\Courses\Controller\CoursesAppCrudController;
use App\Repository\ExportRepository;
use App\Repository\UserCourseRepository;
use App\Service\Course\Common\UserCourseService;
use App\Service\Course\Stats\General\CourseStatsService;
use App\Service\Course\Stats\Persons\CourseItineraryService;
use App\Service\Course\Stats\Persons\CoursePersonStrategy;
use App\Service\Itinerary\General\ItineraryCourseReports;
use App\Service\Notification\EmailNotificationService;
use App\Service\SettingsService;
use App\Service\Task\TaskService;
use App\Service\ZipFileTask\ZipFileTaskService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use EasyCorp\Bundle\EasyAdminBundle\Collection\FieldCollection;
use EasyCorp\Bundle\EasyAdminBundle\Collection\FilterCollection;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Filters;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Config\Option\EA;
use EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\SearchDto;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Orm\EntityRepository;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use FOS\RestBundle\Controller\Annotations as Rest;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Intl\Countries;
use Symfony\Component\Intl\Locales;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

const CACHED_DATA_STATS_ATTRIBUTE = 'data-stats-itinerary';

class ItineraryCrudController extends AbstractCrudController
{
    use SerializerTrait;
    use FilterCategoriesTrait;
    use FiltersClearTrait;
    use VueAppDefaultConfiguration;

    private RequestStack $request;
    private TranslatorInterface $translator;
    private EntityManagerInterface $em;
    private AdminContextProvider $adminContext;
    private LoggerInterface $logger;
    private EntityRepository $entityRepository;
    private AdminUrlGenerator $adminUrlGenerator;
    private JWTManager $JWTManager;
    private SettingsService $settings;
    private EmailNotificationService $emailNotificationService;
    private CourseItineraryService $courseItineraryService;
    private Security $security;
    private UserCourseService $userCourseService;
    private CourseStatsService $courseStatsService;

    private ItineraryCourseReports $itineraryCourseReports;
    private TaskService $taskService;
    private ZipFileTaskService $zipFileTaskService;

    public function __construct(
        TranslatorInterface $translator,
        EntityManagerInterface $entityManager,
        AdminContextProvider $adminContext,
        LoggerInterface $logger,
        EntityRepository $entityRepository,
        RequestStack $request,
        AdminUrlGenerator $adminUrlGenerator,
        JWTManager $JWTManager,
        SettingsService $settingsService,
        EmailNotificationService $emailNotificationService,
        CourseItineraryService $courseItineraryService,
        Security $security,
        UserCourseService $userCourseService,
        CourseStatsService $courseStatsService,
        ItineraryCourseReports $itineraryCourseReports,
        TaskService $taskService,
        ZipFileTaskService $zipFileTaskService
    ) {
        $this->translator = $translator;
        $this->em = $entityManager;
        $this->adminContext = $adminContext;
        $this->logger = $logger;
        $this->entityRepository = $entityRepository;
        $this->request = $request;
        $this->adminUrlGenerator = $adminUrlGenerator;
        $this->JWTManager = $JWTManager;
        $this->settings = $settingsService;
        $this->emailNotificationService = $emailNotificationService;
        $this->courseItineraryService = $courseItineraryService;
        $this->security = $security;
        $this->userCourseService = $userCourseService;
        $this->courseStatsService = $courseStatsService;
        $this->itineraryCourseReports = $itineraryCourseReports;
        $this->taskService = $taskService;
        $this->zipFileTaskService = $zipFileTaskService;
    }

    protected function getUser()
    {
        return $this->security->getUser();
    }

    public static function getEntityFqcn(): string
    {
        return Itinerary::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular($this->translator->trans('itinerary.label_in_singular', [], 'messages', $this->getUser()->getLocale()))
            ->setEntityLabelInPlural($this->translator->trans('itinerary.label_in_plural', [], 'messages', $this->getUser()->getLocale()))
            ->addFormTheme('@FOSCKEditor/Form/ckeditor_widget.html.twig')
            ->addFormTheme('@KMSFroalaEditor/Form/froala_widget.html.twig')
            ->overrideTemplate('crud/detail', 'admin/itinerary/detail.html.twig')
            ->overrideTemplate('crud/index', 'admin/itinerary/app.html.twig')
            ->setSearchFields(['name']);
    }

    public function configureActions(Actions $actions): Actions
    {
        $newEditAction = Action::new(
            'editItineraryAction',
            $this->translator->trans('Edit', [], 'messages', $this->getUser()->getLocale())
        )
            ->linkToCrudAction('editItineraryAction')
            ->setCssClass('btn btn-primary');

        $downloadStatsAction = Action::new(
            'downloadItineraryStatsAction',
            $this->translator->trans('itinerary.users.download_user', [], 'messages', $this->getUser()->getLocale())
        )
            ->linkToCrudAction('downloadItineraryStatsAction')
            ->displayAsButton()
            ->setHtmlAttributes(['data-toggle' => 'modal', 'data-target' => '#modal-export-users'])
            ->setIcon('fa fa-download')
            ->setCssClass('btn btn-primary btn-primary');

        $actions
            ->add(Crud::PAGE_INDEX, Action::DETAIL)
            ->remove(Crud::PAGE_DETAIL, Action::EDIT)
            ->add(Crud::PAGE_DETAIL, $newEditAction)
            ->add(Crud::PAGE_DETAIL, $downloadStatsAction)
            ->reorder(Crud::PAGE_DETAIL, [Action::INDEX, 'downloadItineraryStatsAction', 'editItineraryAction', Action::DELETE]);

        return $actions;
    }

    public function configureFields(string $pageName): iterable
    {
        yield IdField::new('id')
            ->hideOnDetail()
            ->hideOnForm();
        yield TextField::new('name', $this->translator->trans('itinerary.name', [], 'messages', $this->getUser()->getLocale()))
            ->setColumns('col-xs-12 col-md-6')
            ->setTemplatePath('bundles/EasyAdminBundle/field-linked.html.twig');
        yield FosCkeditorField::new('description', $this->translator->trans('itinerary.description', [], 'messages', $this->getUser()->getLocale()))
            ->setColumns(12)
            ->hideOnIndex()
            ->hideOnDetail();
        yield IntegerField::new('totalCourses', $this->translator->trans('itinerary.total_courses', [], 'messages', $this->getUser()->getLocale()))
            ->hideOnForm()
            ->hideOnDetail();
        yield AssociationField::new('createdBy', $this->translator->trans('common_areas.created_by', [], 'messages', $this->getUser()->getLocale()))
            ->hideOnForm()
            ->hideOnDetail();
    }

    public function configureFilters(Filters $filters): Filters
    {
        $filters
            ->add(ItineraryCourseFilter::new('itineraryCourse', $this->translator->trans('course.label_in_plural', [], 'messages', $this->getUser()->getLocale())))
            ->add(ItineraryCreatedByFilter::new('createdBy', $this->translator->trans('common_areas.created_by', [], 'messages', $this->getUser()->getLocale())));

        return $filters;
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        $courseStatus = [UserCourse::STATUS_NO_STARTED, UserCourse::STATUS_STARTED, UserCourse::STATUS_FINISHED];
        $responseParameters->set('courseStatus', $courseStatus);
        if (Crud::PAGE_INDEX == $responseParameters->get('pageName')) {
            $this->configureAppResponseParameters(
                $responseParameters,
                $this->settings,
                $this->adminContext,
                $this->JWTManager,
                [],
                [
                    'urlNewItinerary' => $this->adminUrlGenerator->unsetAll()
                        ->setController(ItineraryCrudController::class)
                        ->setAction('createItineraryForm')
                        ->generateUrl(),
                ]
            );
        } elseif (Crud::PAGE_DETAIL == $responseParameters->get('pageName')) {
            $itineraryRepository = $this->em->getRepository(Itinerary::class);
            $itinerary = $itineraryRepository->find($this->adminContext->getContext()->getEntity()->getPrimaryKeyValue());
            $responseParameters->set('itinerary', $itinerary);

            $locale = $this->getUser()->getLocale();
            $responseParameters->set('translations', [
                'deleteUser' => [
                    'title' => $this->translator->trans('common_areas.confirm_delete', [], 'messages', $locale),
                    'subtitle' => $this->translator->trans('itinerary.user.confirm_delete', [], 'messages', $locale),
                ],
                'export' => [
                    'title' => 'Excel',
                    'filename' => 'Itinerary User Export',
                    'success' => 'File request succeeded',
                    'failure' => 'Failed to request excel file',
                ],
                'user_filter' => [
                    'no_users' => $this->translator->trans('itinerary.no_users', [], 'messages', $locale),
                    'cancel' => $this->translator->trans('common_areas.cancel', [], 'messages', $locale),
                    'save' => $this->translator->trans('common_areas.save', [], 'messages', $locale),
                    'modify_users' => $this->translator->trans('user_filter.modify_users', [], 'messages', $locale),
                    'assign_manually' => $this->translator->trans('user_filter.assign_manually', [], 'messages', $locale),
                    'assign_filters' => $this->translator->trans('user_filter.assign_filters', [], 'messages', $locale),
                    'placeholder_search_user' => $this->translator->trans('announcements.configureFields.placeholder_search_user', [], 'messages', $locale),
                    'apply_filters' => $this->translator->trans('stats.apply_filters', [], 'messages', $locale),
                    'result_found' => $this->translator->trans('announcements.configureFields.result_found', [], 'messages', $locale),
                    'clear_result' => $this->translator->trans('announcements.configureFields.clear_result', [], 'messages', $locale),
                    'add_all' => $this->translator->trans('common_areas.add_all', [], 'messages', $locale),
                    'remove_all' => $this->translator->trans('common_areas.remove_all', [], 'messages', $locale),
                    'find_by' => $this->translator->trans('user_filter.find_by', [], 'messages', $locale),
                    'confirm_delete_all' => [
                        'title' => $this->translator->trans('common_areas.confirm_delete', [], 'messages', $locale),
                        'subtitle' => $this->translator->trans('itinerary.user.confirm_delete_all', [], 'messages', $locale),
                    ],
                    'filters' => $this->translator->trans('menu.users_managment.filter', [], 'messages', $locale),
                ],
                'itinerary_user_filter' => [
                    'no_users' => $this->translator->trans('itinerary.no_users', [], 'messages', $locale),
                    'cancel' => $this->translator->trans('common_areas.cancel', [], 'messages', $locale),
                    'save' => $this->translator->trans('common_areas.save', [], 'messages', $locale),
                    'close' => $this->translator->trans('common_areas.close', [], 'messages', $locale),
                    'modify_users' => $this->translator->trans('itinerary.user.modify_users', [], 'messages', $locale),
                    'assign_manually' => $this->translator->trans('itinerary.user.assign_manual', [], 'messages', $locale),
                    'assign_filters' => $this->translator->trans('itinerary.user.assign_filter', [], 'messages', $locale),
                    'placeholder_search_user' => $this->translator->trans('announcements.configureFields.placeholder_search_user', [], 'messages', $locale),
                    'apply_filters' => $this->translator->trans('stats.apply_filters', [], 'messages', $locale),
                    'result_found' => $this->translator->trans('announcements.configureFields.result_found', [], 'messages', $locale),
                    'clear_result' => $this->translator->trans('announcements.configureFields.clear_result', [], 'messages', $locale),
                    'add_all' => $this->translator->trans('common_areas.add_all', [], 'messages', $locale),
                    'remove_all' => $this->translator->trans('common_areas.remove_all', [], 'messages', $locale),
                    'find_by' => $this->translator->trans('itinerary.user.filter_find_by', [], 'messages', $locale),
                    'confirm_delete_all' => [
                        'title' => $this->translator->trans('common_areas.confirm_delete', [], 'messages', $locale),
                        'subtitle' => $this->translator->trans('itinerary.user.confirm_delete_all', [], 'messages', $locale),
                    ],
                    'filters' => $this->translator->trans('menu.users_managment.filter', [], 'messages', $locale),
                ],
                'manager_filter' => [
                    'no_users' => $this->translator->trans('itinerary.no_users', [], 'messages', $locale),
                    'cancel' => $this->translator->trans('common_areas.cancel', [], 'messages', $locale),
                    'save' => $this->translator->trans('common_areas.save', [], 'messages', $locale),
                    'close' => $this->translator->trans('common_areas.close', [], 'messages', $locale),
                    'modify_users' => $this->translator->trans('itinerary.manager.edit_manager', [], 'messages', $locale),
                    'assign_manually' => $this->translator->trans('itinerary.user.assign_manual', [], 'messages', $locale),
                    'assign_filters' => $this->translator->trans('itinerary.user.assign_filter', [], 'messages', $locale),
                    'placeholder_search_user' => $this->translator->trans('itinerary.manager.find_managers', [], 'messages', $locale),
                    'apply_filters' => $this->translator->trans('stats.apply_filters', [], 'messages', $locale),
                    'result_found' => $this->translator->trans('announcements.configureFields.result_found', [], 'messages', $locale),
                    'clear_result' => $this->translator->trans('announcements.configureFields.clear_result', [], 'messages', $locale),
                    'add_all' => $this->translator->trans('common_areas.add_all', [], 'messages', $locale),
                    'remove_all' => $this->translator->trans('common_areas.remove_all', [], 'messages', $locale),
                    'find_by' => $this->translator->trans('user_filter.find_by', [], 'messages', $locale),
                    'confirm_delete_all' => [
                        'title' => $this->translator->trans('common_areas.confirm_delete', [], 'messages', $locale),
                        'subtitle' => $this->translator->trans('itinerary.manager.confirm_delete_all', [], 'messages', $locale),
                    ],
                ],
                'draggable' => [
                    'position_updated' => $this->translator->trans('itinerary.course.position_updated', [], 'messages', $locale),
                ],
                'save_courses' => [
                    'title' => $this->translator->trans('common_areas.confirm_save', [], 'messages', $locale),
                    'subtitle' => $this->translator->trans('itinerary.course.update_warning', [], 'messages', $locale),
                ],
            ]);

            $filter_categories = $this->getFiltersByCategories();
            $responseParameters->set('filterCategories', $filter_categories);
            $responseParameters->set('totalUsers', $this->totalUsersByManager());

            $tab = $this->adminContext->getContext()->getRequest()->get('tab');
            if (!$tab) {
                $tab = 'courses';
            }

            $responseParameters->set('tab', $tab);
        }

        return $responseParameters;
    }

    private function getFiltersByCategories(): array
    {
        $locale = $this->getUser()->getLocale();
        $data = [];
        $filterCategories = $this->em->getRepository(FilterCategory::class)->findAllOrderBySort();

        foreach ($filterCategories as $category) {
            $filterTranslation = $this->em->getRepository(FilterCategoryTranslation::class)->findOneBy(['translatable' => $category, 'locale' => $locale]);
            $data[] = [
                'id' => $category->getId(),
                'name' => $filterTranslation ? $filterTranslation->getName() : $category->getName(),
                'filters' => $this->getFiltersByCategory($category),
            ];
        }

        return $data;
    }

    private function getFiltersByCategory(FilterCategory $category): array
    {
        $locale = $this->getUser()->getLocale();
        $filters = [];
        $filtersByCategory = $this->em->getRepository(Filter::class)->findBy(['filterCategory' => $category], ['sort' => 'ASC']);

        foreach ($filtersByCategory as $filter) {
            $name = $this->em->getRepository(FilterTranslation::class)->getNameTranslation($filter->getId(), $locale);
            $filters[] = [
                'id' => $filter->getId(),
                'name' => $name ?? $filter->getName(),
            ];
        }

        return $filters;
    }

    private function totalUsersByManager(): int
    {
        if ($this->getUser()->isAdmin()) {
            return $this->em->getRepository(User::class)->count(['isActive' => true]);
        }

        $filters = [];
        foreach ($this->getUser()->getFilters() as $filter) {
            $filters[$filter->getFilterCategory()->getId()][] = $filter->getId();
        }

        return $this->em->getRepository(User::class)->countUsersByManager($this->getUser(), $filters);
    }

    public function createIndexQueryBuilder(SearchDto $searchDto, EntityDto $entityDto, FieldCollection $fields, FilterCollection $filters): QueryBuilder
    {
        $qb = $this->entityRepository->createQueryBuilder($searchDto, $entityDto, $fields, $filters);

        if (!\in_array('ROLE_ADMIN', $this->getUser()->getRoles())) {
            $user = $this->getUser();
            $qb->leftJoin('entity.itineraryManagers', 'im');
            $qb->andWhere('entity.createdBy = :user OR im.user = :user')
                ->setParameter('user', $user);
        }

        return $qb;
    }

    /**
     * @Rest\Get("/admin/itinerary/itineraries/{page}", requirements={"page"="\d+"})
     *
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function getItineraries(Request $request, int $page): Response
    {
        $query = $this->em->getRepository(Itinerary::class)->createQueryBuilder('i')
            ->join('i.createdBy', 'u')
            ->leftJoin('i.itineraryCourses', 'ic');

        $user = $this->getUser();
        if (!\in_array('ROLE_ADMIN', $user->getRoles())) {
            $query->leftJoin('i.itineraryManagers', 'im');
            $query->andWhere('i.createdBy = :user OR im.user = :user')
                ->setParameter('user', $user);
        }

        $filterQuery = $request->get('query');
        if (!empty($filterQuery)) {
            $query->andWhere('i.name LIKE :query')
                ->setParameter('query', "%$filterQuery%");
        }

        if ($this->isValidTagList($request->get('tags'))) {
            $tagIDs = $request->get('tags') ? json_decode($request->get('tags'), false) : [];
            foreach ($tagIDs as $index => $tagID) {
                $param = ":filterTagID$index";
                $query->andWhere("i.tags LIKE $param")->setParameter($param, "%[$tagID]%");
            }
        }

        $courseId = $request->get('course');
        if (!empty($courseId)) {
            $course = $this->em->getRepository(Course::class)->find($courseId);
            $query->andWhere('ic.course =:course')->setParameter('course', $course);
        }

        $this->activeItinerariesFilter($request->get('status', ''), $query);

        $countQuery = clone $query;
        $totalItems = $countQuery->select('COUNT(distinct(i.id)) as total')->getQuery()->getSingleScalarResult();

        $pageSize = 25;

        $itineraries = $query->select('i.id', 'i.name', 'i.active', 'i.sort')
            ->addSelect('u.id as user_id', 'u.firstName', 'u.lastName')
            ->addSelect('count(ic) as totalCourses')
            ->setMaxResults($pageSize)
            ->setFirstResult(($page - 1) * $pageSize)
            ->groupBy('i.id')
            ->orderBy('i.sort', 'ASC')
            ->addOrderBy('i.id', 'DESC')
            ->getQuery()
            ->getResult();

        foreach ($itineraries as $k => $itinerary) {
            $itineraries[$k]['viewHref'] = $this->getItineraryHref('detail', $itinerary['id']);
            $itineraries[$k]['editHref'] = $this->getItineraryHref('editItineraryAction', $itinerary['id']);
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'items' => $itineraries,
                'total-items' => (int) $totalItems,
            ],
        ]);
    }

    /**
     * @Rest\Get("/admin/itinerary/translation/{id}", requirements={"id"="\d+"})
     */
    public function getItineraryTranslations(Itinerary $itinerary): Response
    {
        $translations = [];
        /** @var ItineraryTranslation $translation */
        foreach ($itinerary->getTranslations() as $translation) {
            $translations[] = [
                'locale' => $translation->getLocale(),
                'name' => $translation->getName() ?? '',
                'description' => $translation->getDescription() ?? '',
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $translations,
        ]);
    }

    private function getItineraryHref(string $action, $itineraryId): string
    {
        return $this->adminUrlGenerator->unsetAll()->setController(ItineraryCrudController::class)
            ->setAction($action)
            ->setEntityId($itineraryId)
            ->generateUrl();
    }

    private function activeItinerariesFilter(string $status, QueryBuilder $query): QueryBuilder
    {
        if (empty($status)) {
            return $query;
        }

        $statusParam = null;

        if ('active' == $status) {
            $statusParam = 1;
        }

        if ('inactive' == $status) {
            $statusParam = 0;
        }

        if (null !== $statusParam) {
            return $query->andWhere('i.active =:active')->setParameter('active', $statusParam);
        }

        return $query;
    }

    /**
     * @Route("/admin/itinerary/find-courses", name="find_itineraru_courses")
     */
    public function findCourses(Request $request): Response
    {
        $user = $this->getUser();
        $courseRepository = $this->em->getRepository(Course::class);
        $coursesManager = $courseRepository->getCoursesManager($user, true);
        $qb = $courseRepository->createQueryBuilder('c')
            ->join('c.typeCourse', 'tc')
            ->andWhere('c.translation is null')
            ->andWhere('tc.id =:type_online')
            ->setParameter('type_online', TypeCourse::TYPE_TELEFORMACION);

        if (\in_array('ROLE_MANAGER', $user->getRoles()) && \count($coursesManager) > 0) {
            $qb->andWhere(':user MEMBER OF c.managers')
                ->setParameter('user', $user);
        }

        $requestData = json_decode($request->getContent(), true);
        if (isset($requestData['searchQuery']) && !empty($requestData['searchQuery'])) {
            $qb->andWhere('c.name LIKE :searchQuery OR c.code LIKE :searchQuery')->setParameter('searchQuery', "%{$requestData['searchQuery']}%");
        }

        if (isset($requestData['exclude_ids']) && !empty($requestData['exclude_ids'])) {
            $qb->andWhere('c.id NOT IN (:excludeIds)')->setParameter('excludeIds', $requestData['exclude_ids']);
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $qb->select(
                'c.id, c.name, c.code, c.image, 
                    cc.id as categoryId, COALESCE(ct.name, cc.name) as category'
            )
                ->join('c.category', 'cc')
                ->leftJoin('cc.translations', 'ct', 'WITH', 'ct.locale = :locale')
                ->andWhere('c.active = 1')
                ->setParameter('locale', $this->getUser()->getLocale())
                ->addOrderBy('c.name', 'ASC')
                ->getQuery()
                ->getResult(),
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Rest\Delete("/admin/itinerary/{id}", requirements={"id"="\d+"})
     */
    public function deleteItinerary(Itinerary $itinerary): Response
    {
        $this->em->remove($itinerary);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => 'DELETED',
        ]);
    }

    /**
     * @Rest\Route("/admin/itinerary/{itinerary}/load-courses", name="load-itinerary-courses")
     */
    public function loadCourses(Itinerary $itinerary): Response
    {
        $user = $this->getUser();
        $courses = $this->em->getRepository(ItineraryCourse::class)->getItineraryCourses($itinerary, $user);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $courses,
        ]);
    }

    /**
     * @Route("/admin/itinerary/add-course", name="add-itinerary-course")
     */
    public function addCourse(Request $request): Response
    {
        $data = json_decode($request->getContent(), true);
        $itineraryRepository = $this->em->getRepository(Itinerary::class);
        /** @var Itinerary $itinerary */
        $itinerary = $itineraryRepository->find($data['itinerary_id']);
        if (!$itinerary) {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => true,
                'data' => 'Itinerary not found',
            ]);
        }

        $course = $this->em->getRepository(Course::class)->find($data['course_id']);
        if (!$course) {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => true,
                'data' => 'Course not found',
            ]);
        }

        $itineraryRepository->addCourse($itinerary, $course);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
        ]);
    }

    /**
     * @Route("/admin/itinerary/remove-course", name="remove-itinerary-course")
     */
    public function removeCourse(Request $request): Response
    {
        $data = json_decode($request->getContent(), true);
        $itineraryRepository = $this->em->getRepository(Itinerary::class);
        /** @var Itinerary $itinerary */
        $itinerary = $itineraryRepository->find($data['itinerary_id']);
        if (!$itinerary) {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => true,
                'data' => 'Itinerary not found',
            ]);
        }

        $course = $this->em->getRepository(Course::class)->find($data['course_id']);
        if (!$course) {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => true,
                'data' => 'Course not found',
            ]);
        }

        $itineraryRepository->removeCourse($itinerary, $course);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/save-courses", name="save-itinerary-courses")
     */
    public function saveCourses(Itinerary $itinerary, Request $request): Response
    {
        $data = json_decode($request->getContent(), true);

        $currentItineraryCourses = $itinerary->getItineraryCourses();
        foreach ($currentItineraryCourses as $current) {
            $found = false;
            foreach ($data['itinerary_courses_id'] as $courseId) {
                if ($current->getCourse()->getId() == $courseId) {
                    $found = true;
                }
            }
            if (!$found) {
                $itinerary->removeItineraryCourse($current);
            }
        }

        $currentItineraryCourses = $itinerary->getItineraryCourses();
        foreach ($data['itinerary_courses_id'] as $courseId) {
            $found = false;
            foreach ($currentItineraryCourses as $current) {
                if ($current->getCourse()->getId() == $courseId) {
                    $found = true;
                }
            }
            if (!$found) {
                $newItineraryCourse = new ItineraryCourse();
                $newItineraryCourse->setItinerary($itinerary);
                $newItineraryCourse->setCourse($this->em->getRepository(Course::class)->find($courseId));
                $newItineraryCourse->setPosition(99);
                $itinerary->addItineraryCourse($newItineraryCourse);
            }
        }

        // Update position
        $position = 1;
        foreach ($itinerary->getItineraryCourses() as $course) {
            $course->setPosition($position);
            ++$position;
        }

        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'message' => 'Itinerary courses updated',
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/delete-course", name="delete-itinerary-course")
     */
    public function deleteCourse(Itinerary $itinerary, Request $request): Response
    {
        $data = json_decode($request->getContent(), true);
        $course = $this->em->getRepository(Course::class)->find($data['id']);
        $itineraryCourse = $this->em->getRepository(ItineraryCourse::class)->findOneBy(['itinerary' => $itinerary, 'course' => $course]);
        $coursePosition = $itineraryCourse->getPosition();
        $itinerary->removeItineraryCourse($itineraryCourse);
        foreach ($itinerary->getItineraryCourses() as $itCourse) {
            if ($itCourse->getPosition() > $coursePosition) {
                $itCourse->setPosition($itCourse->getPosition() - 1);
            }
        }
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'message' => 'Itinerary courses deleted',
        ]);
    }

    /**
     * @Route("/admin/itinerary/course-categories", methods={"GET"}, name="load-itinerary-course-categories")
     *
     * @return Response
     */
    public function loadItineraryCourseCategories()
    {
        $courseRepository = $this->em->getRepository(Course::class);

        $isAdmin = \in_array('ROLE_ADMIN', $this->getUser()->getRoles());
        $canFindAllCourses = $this->settings->get('app.permissions.manager.canFindAllCoursesInItinerary');

        $qb = $courseRepository->createQueryBuilder('c')->andWhere('c.translation is null');
        if (!$isAdmin && !$canFindAllCourses) {
            $qb->andWhere('c.createdBy = :user or :user MEMBER OF c.managers')->setParameter('user', $this->getUser());
        }

        $categories = $qb->select('cc.id, cc.name')
            ->join('c.category', 'cc')
            ->addOrderBy('cc.name', 'ASC')
            ->groupBy('cc.id')
            ->getQuery()
            ->getResult();

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'categoryFilterOptions' => $categories,
            ],
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/update-position", name="update-itinerary-course-position")
     */
    public function updateCoursesPosition(Itinerary $itinerary, Request $request): Response
    {
        $data = json_decode($request->getContent(), true);

        foreach ($data['itinerary_courses'] as $info) {
            $itineraryCourse = $this->em->getRepository(ItineraryCourse::class)->find($info['itinerary_course_id']);

            if ($itineraryCourse) {
                $itineraryCourse->setPosition($info['position']);
                $this->em->persist($itineraryCourse);
            }
        }

        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'message' => 'Position has been updated',
        ]);
    }

    /**
     * @Route("/admin/itinerary/available-filters", name="itinerary-available-filters")
     */
    public function userAvailableFilters(): Response
    {
        $locale = $this->getUser()->getLocale();

        $filters = $this->em->getRepository(User::class)->getUserAvailableFilters($this->getUser());

        $status[] = [
            'name' => $this->translator->trans('stats.export.filter.activeUsers', [], 'messages', $locale),
            'filters' => [[
                'id' => User::ACTIVITY_STATUS_FILTER[User::ALL_USERS],
                'name' => $this->translator->trans('stats.content_allusers', [], 'messages', $locale),
            ], [
                'id' => User::ACTIVITY_STATUS_FILTER[User::ACTIVE_USERS],
                'name' => $this->translator->trans('stats.export.filter.activeUsers_val_yes', [], 'messages', $locale),
            ], [
                'id' => User::ACTIVITY_STATUS_FILTER[User::INACTIVE_USERS],
                'name' => $this->translator->trans('stats.export.filter.activeUsers_val_no', [], 'messages', $locale),
            ]],
        ];

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'filters' => $filters,
                'statusFilters' => $status,
            ],
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/search-users", name="itinerary-search-users")
     */
    public function getAvailableUsers(Itinerary $itinerary, Request $request): Response
    {
        $data = json_decode($request->getContent(), true);
        $filters = $data['filters'] ?? [];
        $userIds = [];
        $managerAssignData = [];

        foreach ($itinerary->getItineraryUsers() as $itineraryUser) {
            $userIds[] = $itineraryUser->getUser()->getId();
        }

        /** @var User $user */
        $user = $this->getUser();

        if ($user->isManager() && $user->getFilters()) {
            // Parse filters to match the expected format in getFilteredUsers()
            foreach ($user->getFilters() as $filter) {
                $categoryName = $filter->getFilterCategory()->getName();

                if (!isset($filters[$categoryName])) {
                    $filters[$categoryName] = [];
                }

                $filters[$categoryName][] = ['id' => $filter->getId()];
            }
        }

        /*if ($user->isManager() && $user->getFilters()) {
            $managerAssignData = $user->getFilters()->toArray();
        }*/

        $users = $this->em->getRepository(User::class)->getFilteredUsers(
            $user,
            $filters,
            $userIds,
            $data['searchQuery'],
            true,
            User::ACTIVITY_STATUS_FILTER[User::ALL_USERS],
            $managerAssignData,
        );

        $page = max((int) $data['page'], 1);
        $pageSize = max((int) $data['pageSize'], 1);

        /** @var QueryBuilder $countQb */
        $countQb = clone $users;
        $parametersBackup = $countQb->getParameters();

        $totalItemsQuery = $this->em->getRepository(User::class)
            ->createQueryBuilder('cu2')
            ->andWhere(
                $users->expr()->in(
                    'cu2.id',
                    $countQb->select('DISTINCT(u.id)')
                        ->getQuery()
                        ->getDQL()
                )
            )
            ->setParameters($parametersBackup);

        $totalItems = (int) $totalItemsQuery
            ->select('COUNT(cu2.id) as total')
            ->getQuery()
            ->getSingleScalarResult();

        $users->select('u.id', 'u.firstName', 'u.lastName', 'u.email', 'u.isActive', 'u.code');

        $users->setMaxResults($pageSize)
            ->setFirstResult(($page - 1) * $pageSize);

        $result = $users->getQuery()->getResult();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'users' => $result,
                'totalItems' => $totalItems,
            ],
        ], ['groups' => 'itinerary']);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/search-team-users", name="itinerary-search-team-users")
     */
    public function getTeamUsers(Itinerary $itinerary, Request $request): Response
    {
        $data = json_decode($request->getContent(), true);

        $userIds = [];
        foreach ($itinerary->getItineraryUsers() as $itineraryUser) {
            $userIds[] = $itineraryUser->getUser()->getId();
        }

        $users = $this->em->getRepository(User::class)->getTeamUsers($itinerary->getCreatedBy(), $data['searchQuery'], $userIds);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'users' => $users,
            ],
        ], ['groups' => 'itinerary']);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/get-users-filters", name="itinerary-get-users-filters")
     */
    public function getUsersFilters(Itinerary $itinerary): Response
    {
        $result = $this->em->getRepository(Itinerary::class)->getUsersFilters($itinerary);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'users' => $result,
            ],
        ], ['groups' => 'itinerary']);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/get-users", name="itinerary-get-users")
     */
    public function getUsers(Itinerary $itinerary): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'users' => $this->em->createQueryBuilder()
                    ->select('u')
                    ->from('App:User', 'u')
                    ->join('u.itineraryUsers', 'iu')
                    ->where('iu.itinerary = :itinerary')
                    ->setParameter('itinerary', $itinerary)
                    ->groupBy('u.id')
                    ->getQuery()
                    ->getResult(),
            ],
        ], ['groups' => 'itinerary']);
    }

    /**
     * @Route("/admin/itinerary/redirect-to-course/{course}", name="itinerary_redirect_to_course", methods={"GET"})
     */
    public function redirectToCourse(
        Course $course
    ): RedirectResponse {
        $activeParams = [
            'id' => $course->getId(),
            'name' => $course->getName(),
        ];

        $encodedActiveParams = base64_encode(json_encode($activeParams));

        $url = $this->adminUrlGenerator
            ->setController(CoursesAppCrudController::class)
            ->setAction('index')
            ->set('activeRoute', 'Course')
            ->set('activeParams', $encodedActiveParams)
            ->generateUrl();

        return $this->redirect($url);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/get-users-paginated", name="itinerary-get-users-paginated", methods={"POST"})
     */
    public function getUsersPaginated(Itinerary $itinerary): Response
    {
        $persons = new CoursePersonStrategy($this->courseItineraryService);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'users' => $persons->getPersons(null, null, $itinerary),
            ],
        ], ['groups' => 'itinerary']);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/add-user", name="itinerary-add-user")
     */
    public function addUser(Itinerary $itinerary, Request $request): Response
    {
        $data = json_decode($request->getContent(), true);

        $user = $this->em->getRepository(User::class)->find($data['user_id']);
        $itineraryUser = new ItineraryUser();
        $itineraryUser->setItinerary($itinerary);
        $itineraryUser->setUser($user);

        $itinerary->addItineraryUser($itineraryUser);
        $this->em->flush();

        $this->createNotificationItinerary($user, $itinerary);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'message' => $this->translator->trans('itinerary.user.add_success', [], 'messages', $this->getUser()->getLocale()),
        ]);
    }

    private function createNotificationItinerary(User $user, Itinerary $itinerary): void
    {
        if (!$itinerary->isActive()) {
            return;
        }

        if (0 == \count($itinerary->getItineraryCourses())) {
            return;
        }

        $attributes[] = [
            '%user%' => $user->getFullName(),
            '%itinerary%' => $itinerary->getName(),
        ];

        $parameters = [];
        $parameters['type'] = 'itinerary';
        $parameters['translationTitle'] = 'notification.itinerary.title';
        $parameters['translationText'] = 'notification.itinerary.subject';
        $parameters['attributes'] = $attributes;
        $parameters['userId'] = $user->getId();
        $parameters['itineraryId'] = $itinerary->getId();

        $emailNotification = $this->em->getRepository(EmailNotification::class)->findOneBy(['type' => 'itinerary', 'user' => $user, 'itinerary' => $itinerary]);
        if (!$emailNotification) {
            $this->emailNotificationService->insertNotificationWithTranslation($parameters);
        }
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/remove-user", name="itinerary-remove-user")
     */
    public function removeUser(Itinerary $itinerary, Request $request): Response
    {
        $data = json_decode($request->getContent(), true);

        $user = $this->em->getRepository(User::class)->find($data['user_id']);
        $itineraryUser = $this->em->getRepository(ItineraryUser::class)->findOneBy(['itinerary' => $itinerary, 'user' => $user]);
        $itinerary->removeItineraryUser($itineraryUser);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'message' => $this->translator->trans('itinerary.user.remove_success', [], 'messages', $this->getUser()->getLocale()),
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/user-proogress-details/{user}", name="itinerary-user-proogress-details")
     *
     * @throws NonUniqueResultException
     */
    public function userProgressDetails(Itinerary $itinerary, User $user, UserCourseRepository $userCourseRepository): Response
    {
        $opinionsAvailable = $this->settings->get('app.opinions.platform');
        $locale = $this->getUser()->getLocale();

        $userCoursesProgress = [];
        foreach ($itinerary->getItineraryCourses() as $itineraryCourse) {
            $course = $itineraryCourse->getCourse();
            $userCourse = $userCourseRepository->getUserCourseTranslated($course, $user);
            $userCourseChaptersProgress = [];
            $details = [];
            $totalTime = 0;

            $status = 'unstarted';
            $completed = false;

            if ($userCourse) {
                $status = $userCourse->getFinishedAt() ? 'completed' : 'started';
                // $completed = $opinionsAvailable ? ($userCourse->getFinishedAt() && $userCourse->getValuedAt()) : (bool) $userCourse->getFinishedAt();
                $completed = (bool) $userCourse->getFinishedAt();
                $totalChaptersFinished = 0;
                $course = $userCourse->getCourse();
                $courseParams = $this->courseStatsService->getParamsCourse($course);
                $chapters = $this->em->getRepository(UserCourseChapter::class)->getUserCourseChapters($userCourse);
                foreach ($chapters as $chapter) {
                    $queryParamsChapter = $this->courseStatsService->getChapterQueryParameters($chapter->getChapter(), [$userCourse->getUser()->getId()], $courseParams);

                    $timeByChapter = $this->em->getRepository(UserCourseChapter::class)->getTimeByChapter($queryParamsChapter);
                    $totalTime += \intval($timeByChapter['totalTime'] ?? 0);
                    if ($chapter->getFinishedAt()) {
                        ++$totalChaptersFinished;
                    }
                }
                $userCourseChaptersProgress = ['totalChapters' => $chapters ? \count($chapters) : 0, 'finished' => $totalChaptersFinished, 'totalTime' => $totalTime];
                $details = [
                    'details' => $this->userCourseService->getUserData($course, $user, null),
                    'courseData' => $this->userCourseService->courseDataDetailsStats($course),
                ];
            }

            $message = $this->translator->trans('itinerary.status.' . $status, [], 'messages', $locale);

            $userCoursesProgress[] = [
                'ucid' => $userCourse ? $userCourse->getId() : null,
                'courseId' => $course->getId(),
                'name' => $course->getName(),
                'image' => $course->getImage(),
                'finished' => $userCourse ? $userCourse->getFinishedAt() : null,
                'started' => $userCourse ? $userCourse->getStartedAt() : null,
                'status' => $status,
                'message' => $message,
                'completed' => $completed,
                'chaptersProgress' => $userCourseChaptersProgress,
                'details' => $details,
            ];
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $userCoursesProgress,
        ];

        return $this->sendResponse($response, ['groups' => ['details', 'detail', 'progress', 'passport']]);
    }

    private function resetStoreFlashItineraryStatsSummary(Request $request)
    {
        $session = $request->getSession();
        $session->set(CACHED_DATA_STATS_ATTRIBUTE, []);
    }

    private function setStoreFlashItineraryStatsSummary(Itinerary $itinerary, Request $request): array
    {
        // query & store in cache for other calls to avoid repeating same query

        $itineraryRepository = $this->em->getRepository(Itinerary::class);

        $totalCourses = $itineraryRepository->getCoursesTotalFromItinerary($itinerary);
        $totalUsersManual = $itineraryRepository->getUsersTotalFromItinerary($itinerary);
        $totalUsersFilter = 0; // $itineraryRepository->getUsersTotalFromItinerary($itinerary);
        $totalUsers = $totalUsersManual + $totalUsersFilter;
        $totalUsersCompleted = 0;
        $totalUsersStarted = 0;
        $totalUsersTime = 0;

        $statsUsers = $itineraryRepository->getUsersTotalsFromItineraryByCompletionStatus($itinerary);
        foreach ($statsUsers as $statsUser) {
            if ($statsUser['completed'] == $totalCourses) {
                ++$totalUsersCompleted;
            } elseif ($statsUser['completed'] > 0 || $statsUser['started'] > 0) {
                ++$totalUsersStarted;
            }
            $totalUsersTime += $statsUser['total_time'];
        }

        $coursesStats = [];
        $coursesStats['totalCourses'] = $totalCourses;
        $usersStats = [];
        $usersStats['totalUsers'] = $totalUsers;
        $usersStats['totalUsersTime'] = $totalUsersTime;
        $usersStats['totalUsersCompleted'] = $totalUsersCompleted;
        $usersStats['totalUsersStarted'] = $totalUsersStarted;
        $usersStats['statsUsers'] = $statsUsers;
        $dataStats = [];
        $dataStats['coursesStats'] = $coursesStats;
        $dataStats['usersStats'] = $usersStats;

        $session = $request->getSession();
        $session->set(CACHED_DATA_STATS_ATTRIBUTE, $dataStats);

        return $dataStats;
    }

    private function getStoreFlashItineraryStatsSummary(Itinerary $itinerary, Request $request): array
    {
        $session = $request->getSession();
        $dataStats = $session->get(CACHED_DATA_STATS_ATTRIBUTE, []);
        if (empty($dataStats)) {
            $dataStats = $this->setStoreFlashItineraryStatsSummary($itinerary, $request);
        }

        return $dataStats;
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/stats-summary-paginated/{page}", name="itinerary-stats-summary-paginated", methods={"POST"})
     */
    public function getItineraryStatsSummaryPaginated(Itinerary $itinerary, int $page, Request $request): Response
    {
        $itineraryRepository = $this->em->getRepository(Itinerary::class);
        $limit = 5;

        $dataStats = $itineraryRepository->getUsersTotalsPaginated($itinerary, $page, $limit);
        $paginator = $dataStats['paginator'];

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $paginator,
            'page' => $page,
            'maxPages' => $dataStats['maxPages'],
            'allResults' => $dataStats['allResults'],
            'query' => $dataStats['query'],
        ], [
            'groups' => ['progress'],
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/users-all", name="itinerary-users-all")
     *
     * @return Response
     */
    public function getTotalUsersFromItinerary(Itinerary $itinerary)
    {
        $itineraryRepository = $this->em->getRepository(Itinerary::class);

        $assignedByHand = $itineraryRepository->getTotalUsersIdsAssignedByHand($itinerary);

        $assignedByFilters = $itineraryRepository->getTotalUsersAssignedByFilters($itinerary);

        // STORE ARRAY OF USER_IDS IN SESSION FOR LATER USE IN OTHER QUERIES

        $totalAssignedByHand = \count($assignedByHand);
        $totalAssignedByFilters = \count($assignedByFilters);
        $total = $totalAssignedByHand + $totalAssignedByFilters;

        $data = [];
        $data['totalByHand'] = $totalAssignedByHand;
        $data['totalByFilters'] = $totalAssignedByFilters;
        $data['total'] = $total;

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $data,
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/users-completed", name="itinerary-users-completed")
     *
     * @return Response
     */
    public function getUsersWithCompletedItinerary(Itinerary $itinerary)
    {
        $itineraryRepository = $this->em->getRepository(Itinerary::class);

        $totalCourses = \count($itinerary->getItineraryCourses());

        $results = $itineraryRepository->getUsersWithCompletedItinerary($itinerary, $totalCourses);

        // STORE ARRAY OF USER_IDS IN SESSION FOR LATER USE IN OTHER QUERIES

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $results,
            'total' => \count($results),
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/users-started", name="itinerary-users-started")
     *
     * @return Response
     */
    public function getUsersWithStartedItinerary(Itinerary $itinerary)
    {
        $itineraryRepository = $this->em->getRepository(Itinerary::class);

        $totalCourses = \count($itinerary->getItineraryCourses());

        $results = $itineraryRepository->getUsersWithStartedItinerary($itinerary);

        // STORE ARRAY OF USER_IDS IN SESSION FOR LATER USE IN OTHER QUERIES

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $results,
            'total' => \count($results),
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/stats-summary/{bFullReload}", name="itinerary-stats-summary")
     */
    public function getItineraryStatsSummary(Itinerary $itinerary, bool $bFullReload, Request $request): Response
    {
        if (true == $bFullReload) {
            $this->resetStoreFlashItineraryStatsSummary($request);
        }
        // get data from cache. If it is not stored, request it and save it
        $dataStats = $this->getStoreFlashItineraryStatsSummary($itinerary, $request);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $dataStats,
            'full' => $bFullReload,
        ], [
            'groups' => ['progress'],
        ]);
    }

    private function statisticsByFilter(Itinerary $itinerary, $categoryId, Request $request): Response
    {
        $itineraryRepository = $this->em->getRepository(Itinerary::class);

        $conditions = $this->getConditions();
        $filterCategory = $this->em->getRepository(FilterCategory::class)->find($categoryId);

        $itineraryFilters = $itinerary->getFiltersGroupedByCategory();

        $filterCategories = $this->em->getRepository(FilterCategory::class)->findAll();
        foreach ($filterCategories as $category) {
            if (!isset($itineraryFilters[$category->getId()])) {
                if ($category->getParent()) {
                    $itineraryFilters[$category->getId()] = $this->em->getRepository(Filter::class)->findBy(['parent' => [4, 12]]);
                }
            }
        }

        $filters = $itineraryFilters[$categoryId] ?? [];

        $hotels = [];
        $hotelsName = [];
        $dataCompleted = [];
        $dataStarted = [];
        $dataUnstarted = [];
        $totalCourses = \count($itinerary->getItineraryCourses());

        $users = [];

        foreach ($filters as $filter) {
            $totalCompleted = 0;
            $totalStarted = 0;
            $totalUnstarted = 0;
            $usersManual = 0;
            $usersItinerary = 0;

            $conditions['filters'] = [$filter->getId()];

            $usersManual = $itineraryRepository->getUsersPaginated($itinerary, $conditions);
            $usersItinerary = $itineraryRepository->getUsersByItineraryFilters($itinerary);

            foreach ([$usersManual, $usersItinerary] as $list) {
                if (!\is_array($list)) {
                    continue;
                }

                foreach ($list as $user) {
                    if (isset($users[$user['id']])) {
                        continue;
                    }

                    $users[$user['id']] = $user;
                    if ($user['completed'] == $totalCourses) {
                        ++$totalCompleted;
                    } elseif ($user['completed'] > 0 || $user['started'] > 0) {
                        ++$totalStarted;
                    } else {
                        ++$totalUnstarted;
                    }
                }
            }

            $hotels[] = [
                'filter' => $filter->getName(),
                'completed' => $totalCompleted,
                'started' => $totalStarted,
                'unstarted' => $totalUnstarted,
                'total' => $totalCompleted + $totalStarted + $totalUnstarted,
            ];
        }

        // get data from cache. If it is not stored, request it and save it
        /*

        $dataStats = $this->getStoreFlashItineraryStatsSummary($itinerary, $request);

        $usersStats = $dataStats['usersStats'];
        $usersTotal = $usersStats['totalUsers'];
        $totalUsersCompleted = $usersStats['totalUsersCompleted'];
        $totalUsersStarted = $usersStats['totalUsersStarted'];

        */

        // get totals to calculate unassigned
        $totalUsersCompleted = 0;
        $totalUsersStarted = 0;
        $usersTotal = 0;

        $usersTotal = $conditions['totalUsers'];
        $totalUsersCompleted = $conditions['totalUsersCompleted'];
        $totalUsersStarted = $conditions['totalUsersStarted'];

        $unassignedCompleted = $totalUsersCompleted;
        $unassignedStarted = $totalUsersStarted;
        $unassignedUnstarted = \intval($usersTotal) - $unassignedCompleted - $unassignedStarted;

        foreach ($hotels as $hotel) {
            $hotelsName[] = $hotel['filter'];
            $dataCompleted[] = $hotel['completed'];
            $dataStarted[] = $hotel['started'];
            $dataUnstarted[] = $hotel['unstarted'];

            $unassignedCompleted -= \intval($hotel['completed']);
            $unassignedStarted -= \intval($hotel['started']);
            $unassignedUnstarted -= \intval($hotel['unstarted']);
        }

        $hotelsName[] = 'Sin Asignar';
        $dataCompleted[] = $unassignedCompleted;
        $dataStarted[] = $unassignedStarted;
        $dataUnstarted[] = $unassignedUnstarted;

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'total' => \count($hotels),
                'series' => [
                    ['name' => $this->translator->trans('itinerary.status.completed', [], 'messages'), 'data' => $dataCompleted],
                    ['name' => $this->translator->trans('itinerary.status.started', [], 'messages'), 'data' => $dataStarted],
                    ['name' => $this->translator->trans('itinerary.status.unstarted', [], 'messages'), 'data' => $dataUnstarted],
                ],
                'categories' => $hotelsName,
            ],
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/countries-statistics", name="itinerary-countries-statistics")
     */
    public function countriesStatistics(Itinerary $itinerary, Request $request): Response
    {
        // TODO Quick return with default values to prevent timeoutsr.
        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'total' => 0,
                'series' => [
                    ['name' => $this->translator->trans('itinerary.status.completed', [], 'messages'), 'data' => 0],
                    ['name' => $this->translator->trans('itinerary.status.started', [], 'messages'), 'data' => 0],
                    ['name' => $this->translator->trans('itinerary.status.unstarted', [], 'messages'), 'data' => 0],
                ],
                'categories' => [''],
            ],
        ];

        return $this->sendResponse($response);

        return $this->statisticsByFilter($itinerary, 1, $request); // 1 = País
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/hotels-statistics", name="itinerary-hotels-statistics")
     */
    public function hotelsStatistics(Itinerary $itinerary, Request $request): Response
    {
        // TODO Quick return with default values to prevent timeoutsr.
        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'total' => 0,
                'series' => [
                    ['name' => $this->translator->trans('itinerary.status.completed', [], 'messages'), 'data' => 0],
                    ['name' => $this->translator->trans('itinerary.status.started', [], 'messages'), 'data' => 0],
                    ['name' => $this->translator->trans('itinerary.status.unstarted', [], 'messages'), 'data' => 0],
                ],
                'categories' => [''],
            ],
        ];

        return $this->sendResponse($response);

        $this->statisticsByFilter($itinerary, 2, $request); // 2 = Hotel
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/courses-statistics", name="itinerary-courses-statistics")
     */
    public function coursesStatistics(Itinerary $itinerary, Request $request): Response
    {
        // TODO Quick return with default values to prevent timeoutsr.
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'series' => [
                    ['name' => $this->translator->trans('itinerary.status.completed', [], 'messages'), 'data' => 0],
                    ['name' => $this->translator->trans('itinerary.status.started', [], 'messages'), 'data' => 0],
                    ['name' => $this->translator->trans('itinerary.status.unstarted', [], 'messages'), 'data' => 0],
                ],
                'categories' => [],
            ],
        ]);

        $rows = $this->em->getRepository(Itinerary::class)->getCoursesCompletionStats($itinerary);

        $categories = [];
        $completed = [];
        $started = [];
        $unstarted = [];

        foreach ($rows as $row) {
            $categories[] = $row['course'];
            $completed[] = (int) $row['completed'];
            $started[] = (int) $row['started'];
            $unstarted[] = (int) $row['unstarted'];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'series' => [
                    ['name' => $this->translator->trans('itinerary.status.completed', [], 'messages'), 'data' => $completed],
                    ['name' => $this->translator->trans('itinerary.status.started', [], 'messages'), 'data' => $started],
                    ['name' => $this->translator->trans('itinerary.status.unstarted', [], 'messages'), 'data' => $unstarted],
                ],
                'categories' => $categories,
            ],
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/departments-statistics", name="itinerary-departments-statistics")
     */
    public function departmentsStatistics(Itinerary $itinerary, Request $request): Response
    {
        // TODO Quick return with default values to prevent timeoutsr.
        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'total' => 0,
                'series' => [
                    ['name' => $this->translator->trans('itinerary.status.completed', [], 'messages'), 'data' => 0],
                    ['name' => $this->translator->trans('itinerary.status.started', [], 'messages'), 'data' => 0],
                    ['name' => $this->translator->trans('itinerary.status.unstarted', [], 'messages'), 'data' => 0],
                ],
                'categories' => [''],
            ],
        ];

        return $this->sendResponse($response);

        return $this->statisticsByFilter($itinerary, 3, $request); // 3 = Departamento
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/grouping-statistics", name="itinerary-grouping-statistics")
     */
    public function groupingStatistics(Itinerary $itinerary, Request $request): Response
    {
        // TODO Quick return with default values to prevent timeoutsr.
        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'total' => 0,
                'series' => [
                    ['name' => $this->translator->trans('itinerary.status.completed', [], 'messages'), 'data' => 0],
                    ['name' => $this->translator->trans('itinerary.status.started', [], 'messages'), 'data' => 0],
                    ['name' => $this->translator->trans('itinerary.status.unstarted', [], 'messages'), 'data' => 0],
                ],
                'categories' => [''],
            ],
        ];

        return $this->sendResponse($response);

        return $this->statisticsByFilter($itinerary, 5, $request); // 5 = Agrupación
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/add-users", name="itinerary-add-users")
     */
    public function addUsers(Itinerary $itinerary, Request $request): Response
    {
        $data = json_decode($request->getContent(), true);

        $idsToAdd = [];
        $itineraryUsers = $itinerary->getItineraryUsers();
        foreach ($data['user_ids'] as $userId) {
            $found = false;
            foreach ($itineraryUsers as $itineraryUser) {
                if ($itineraryUser->getUser()->getId() == $userId) {
                    $found = true;
                    continue;
                }
            }
            if (!$found) {
                $idsToAdd[] = $userId;
            }
        }

        foreach ($idsToAdd as $userId) {
            $user = $this->em->getRepository(User::class)->find($userId);

            $itineraryUser = new ItineraryUser();
            $itineraryUser->setUser($user);
            $itineraryUser->setItinerary($itinerary);
            $itinerary->addItineraryUser($itineraryUser);

            $this->createNotificationItinerary($user, $itinerary);
        }
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'message' => $this->translator->trans('itinerary.user.add_success', [], 'messages', $this->getUser()->getLocale()),
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/remove-users", name="itinerary-remove-users")
     */
    public function removeAllUsers(Itinerary $itinerary): Response
    {
        $itinerary->removeAllItineraryUser();
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'message' => $this->translator->trans('itinerary.user.remove_success', [], 'messages', $this->getUser()->getLocale()),
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/export-users", name="itinerary-export-users")
     */
    public function exportUsers(Itinerary $itinerary, Request $request): Response
    {
        $exportRepository = $this->em->getRepository(Export::class);
        $filename = $request->get('filename') ?: 'Itinerary users export';
        if ($this->settings->get('app.export.active_cron_exports')) {
            return $this->sendResponse(
                $this->taskService->enqueueTask(
                    $this->getUser(),
                    'export-file',
                    ['itinerary-id' => $itinerary->getId()],
                    'itinerary-user-export',
                    $filename
                )
            );
        }
    }

    /**
     * ************************************
     * Managers.
     * ************************************
     */

    /**
     * @Route("/admin/itinerary/{itinerary}/search-managers", name="itinerary-search-managers")
     */
    public function getAvailableManagers(Itinerary $itinerary, Request $request): Response
    {
        $data = json_decode($request->getContent(), true);
        $managersIds = [];
        foreach ($itinerary->getItineraryManagers() as $manager) {
            $managersIds[] = $manager->getUser()->getId();
        }

        $managers = $this->em->getRepository(User::class)->getFilteredManagers(
            $data['searchQuery'] ?? null,
            $managersIds
        );

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'users' => $managers,
            ],
        ], ['groups' => 'itinerary']);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/get-managers", name="itinerary-get-managers")
     */
    public function getManagers(Itinerary $itinerary): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'users' => $this->em->createQueryBuilder()
                    ->select('u')
                    ->from('App:User', 'u')
                    ->join('u.itineraryManagers', 'im')
                    ->where('im.itinerary = :itinerary')
                    ->setParameter('itinerary', $itinerary)
                    ->groupBy('u.id')
                    ->getQuery()
                    ->getResult(),
            ],
        ], ['groups' => 'itinerary']);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/add-manager", name="itinerary-add-manager")
     */
    public function addManager(Itinerary $itinerary, Request $request): Response
    {
        $data = json_decode($request->getContent(), true);

        $user = $this->em->getRepository(User::class)->find($data['user_id']);
        $itineraryManager = new ItineraryManager();
        $itineraryManager->setItinerary($itinerary);
        $itineraryManager->setUser($user);

        $itinerary->addItineraryManager($itineraryManager);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'message' => $this->translator->trans('itinerary.manager.add_success', [], 'messages', $this->getUser()->getLocale()),
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/remove-manager", name="itinerary-remove-manager")
     */
    public function removeManager(Itinerary $itinerary, Request $request): Response
    {
        $data = json_decode($request->getContent(), true);

        $user = $this->em->getRepository(User::class)->find($data['user_id']);
        $itineraryManager = $this->em->getRepository(ItineraryManager::class)->findOneBy(['itinerary' => $itinerary, 'user' => $user]);
        $itinerary->removeItineraryManager($itineraryManager);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'message' => $this->translator->trans('itinerary.manager.remove_success', [], 'messages', $this->getUser()->getLocale()),
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/add-managers", name="itinerary-add-managers")
     */
    public function addManagers(Itinerary $itinerary, Request $request): Response
    {
        $data = json_decode($request->getContent(), true);

        $idsToAdd = [];
        $itineraryManager = $itinerary->getItineraryManagers();
        foreach ($data['user_ids'] as $userId) {
            $found = false;
            foreach ($itineraryManager as $manager) {
                if ($manager->getUser()->getId() == $userId) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                $idsToAdd[] = $userId;
            }
        }

        $userRepository = $this->em->getRepository(User::class);
        foreach ($idsToAdd as $userId) {
            $itineraryManager = new ItineraryManager();
            $itineraryManager->setUser($userRepository->find($userId));
            $itineraryManager->setItinerary($itinerary);
            $itinerary->addItineraryManager($itineraryManager);
        }

        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'message' => $this->translator->trans('itinerary.manager.add_success', [], 'messages', $this->getUser()->getLocale()),
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/remove-all-managers", name="itinerary-remove-all-managers")
     */
    public function removeAllManagers(Itinerary $itinerary): Response
    {
        $itinerary->removeAllItineraryManagers();
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'message' => $this->translator->trans('itinerary.manager.remove_success', [], 'messages', $this->getUser()->getLocale()),
        ]);
    }

    /**
     * Filters.
     */

    /**
     * @Route("/admin/itinerary/{itinerary}/selected-filters", name="itinerary-selected-filters")
     *
     * @return Response
     */
    public function getAllFiltersForUpdate(Itinerary $itinerary)
    {
        $user = $this->getUser();
        $itineraryFilters = $this->em->getRepository(Filter::class)->getFiltersByItinerary($itinerary);
        $data = [];
        $managerFilters = [];

        if ($user->isManager()) {
            $managerFilters = array_map(function ($filter) {
                return $filter->getId();
            }, $user->getFilters()->toArray());
        }

        foreach ($itineraryFilters as $filter) {
            $filterData = [
                'id' => $filter['filterId'],
                'name' => $filter['name'],
            ];

            if ($user->isManager()) {
                $filterData['isManagerFilter'] = !empty($managerFilters) && \in_array($filter['filterId'], $managerFilters);
            }

            $data['category_' . $filter['categoryId']][] = $filterData;
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Rest\Post("/admin/itinerary/{itinerary}/save-selected-filters", name="itinerary-save-selected-filters")
     */
    public function saveSelectedFilters(Itinerary $itinerary, Request $request): Response
    {
        $content = json_decode($request->getContent(), true);
        $filters = [];

        foreach ($content as $values) {
            foreach ($values as $v) {
                $f = $this->em->getRepository(Filter::class)->find($v['id']);
                if ($f) {
                    $filters[] = $f;
                }
            }
        }

        $itinerary->setFilters($filters);
        $this->em->persist($itinerary);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/filters/{category}", name="itinerary-filters")
     */
    public function getFilters(Itinerary $itinerary, FilterCategory $category): Response
    {
        $filters = [];
        foreach ($itinerary->getFilters() as $filter) {
            if ($filter->getFilterCategory()->getId() == $category->getId()) {
                $filters[] = $filter;
            }
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $filters,
        ], [
            'groups' => ['itinerary'],
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/add-filter", name="itinerary-add-filter")
     */
    public function addFilter(Itinerary $itinerary, Request $request): Response
    {
        $data = json_decode($request->getContent(), true);
        $filter = $this->em->getRepository(Filter::class)->find($data['id']);

        $itinerary->addFilter($filter);
        $this->em->flush();

        return $this->sendResponse(
            [
                'status' => Response::HTTP_OK,
                'message' => $this->translator->trans('itinerary.filter.added', [], 'messages', $this->getUser()->getLocale()),
            ]
        );
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/remove-filter", name="itinerary-remove-filter")
     */
    public function removeFilter(Itinerary $itinerary, Request $request): Response
    {
        $data = json_decode($request->getContent(), true);
        $filter = $this->em->getRepository(Filter::class)->find($data['id']);
        $itinerary->removeFilter($filter);
        $this->em->flush();

        return $this->sendResponse(
            [
                'status' => Response::HTTP_OK,
                'message' => $this->translator->trans('itinerary.filter.removed', [], 'messages', $this->getUser()->getLocale()),
            ]
        );
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/user-in-filters", name="itinerary-user-in-filters")
     */
    public function getUsersInItineraryFilters(Itinerary $itinerary, Request $request): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $itineraryRepository = $this->em->getRepository(Itinerary::class);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $itineraryRepository->getUsersByItineraryFilters($itinerary, $user->isManager() ? $user : null),
        ]);
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/time-spent", name="itinerary-user-time-spent")
     */
    public function getItineraryTimeSpent(Itinerary $itinerary, Request $request): Response
    {
        $itineraryRepository = $this->em->getRepository(Itinerary::class);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $itineraryRepository->getTimeSpentInCourses($itinerary),
        ], [
            'groups' => ['progress'],
        ]);
    }

    /**
     * Stats.
     */
    /**
     * @Route("/admin/stats/itineraryClosedByCountry", name="itinerary-stats-country")
     */
    public function closedByCountry(Request $request): Response
    {
        try {
            $itineraryRepository = $this->em->getRepository(Itinerary::class);
            $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

            $data = $itineraryRepository->findItineraryCloseByCountry($conditions);

            $countries = array_flip($this->settings->get('app.user.extrafields')['country']['options']['choices']);
            $dataCountires = Countries::getNames();
            $dataCountiresC3 = Countries::getAlpha3Names();

            foreach ($data as &$valor) {
                if (isset($countries[$valor['country']])) {
                    $valor['countryName'] = $countries[$valor['country']];
                } elseif (isset($dataCountires[$valor['country']])) {
                    $valor['countryName'] = $dataCountires[$valor['country']];
                } elseif (isset($dataCountiresC3[$valor['country']])) {
                    $valor['countryName'] = $dataCountiresC3[$valor['country']];
                } else {
                    $valor['countryName'] = $valor['country'];
                }
            }

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $data,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Stats.
     */
    /**
     * @Route("/admin/stats/departmentMoreCourse", name="deparment-more-course")
     */
    public function deparmentMoreCourse(Request $request): Response
    {
        try {
            $conditions = empty($this->getConditions()) ? [] : $this->getConditions();
            $itineraryRepository = $this->em->getRepository(Department::class);
            $data = $itineraryRepository->top10DeparmentMoreUse($conditions);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $data,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Stats.
     */
    /**
     * @Route("/admin/stats/courseByDepartment", name="course-by-department")
     */
    public function courseByDepartment(Request $request): Response
    {
        try {
            $itineraryRepository = $this->em->getRepository(Department::class);
            $data = $itineraryRepository->courseByDepartment();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $data,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Stats.
     */
    /**
     * @Route("/admin/stats/itineraryClosedAndProgress", name="itinerary-stats-close-and-progress")
     */
    public function itineraryClosedAndProgress(Request $request): Response
    {
        try {
            $conditions = $this->getConditions();
            $itineraryRepository = $this->em->getRepository(Itinerary::class);
            $open = $itineraryRepository->itineraryClosedAndProgress($conditions, false);
            $close = $itineraryRepository->itineraryClosedAndProgress($conditions, true);

            foreach ($open as &$valorOpen) {
                $name = $valorOpen['name'];
                $newClosedValue = 0;
                foreach ($close as &$valorClosed) {
                    if ($name == $valorClosed['name']) {
                        $newClosedValue = $valorClosed['count'];
                        break;
                    }
                }
                $valorOpen['countOpen'] = $valorOpen['count'];
                $valorOpen['countClosed'] = $newClosedValue;
            }

            foreach ($open as &$valorOpen) {
                unset($valorOpen['count']);
            }
            $data = $open;

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $data,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Get("/admin/itinerary/{id}/generate-url")
     */
    public function itineraryGenerateDetailPage(Request $request, AdminUrlGenerator $adminUrlGenerator, Itinerary $itinerary): Response
    {
        $action = $request->get('action');

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $adminUrlGenerator->unsetAll()->setController(ItineraryCrudController::class)
                ->setAction($action)
                ->setEntityId($itinerary->getId())
                ->generateUrl(),
        ]);
    }

    public function createItineraryForm(): Response
    {
        return $this->renderItineraryApp();
    }

    public function editItineraryAction(AdminContext $context): Response
    {
        $itinerary = $this->em->getRepository(Itinerary::class)->find($context->getEntity()->getPrimaryKeyValue());
        if (!$itinerary) {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => true,
                'data' => 'Not Found',
            ]);
        }

        $tags = [];
        $itineraryTags = explode(',', $itinerary->getTags() ?? '');
        foreach ($itineraryTags as $itineraryTag) {
            $filter = $this->em->getRepository(Filter::class)->find(preg_replace('/[\W]/', '', $itineraryTag));
            if ($filter) {
                $tags[] = [
                    'id' => $filter->getId(),
                    'name' => $filter->getName(),
                ];
            }
        }

        return $this->renderItineraryApp('CreateItinerary', [
            'id' => $itinerary->getId(),
            'name' => $itinerary->getName(),
            'description' => $itinerary->getDescription(),
            'tags' => $tags,
            'active' => $itinerary->isActive(),
        ]);
    }

    /**
     * @param array $routeParams Only if the $starterRoute route require specific params
     */
    private function renderItineraryApp(string $activeRoute = 'CreateItinerary', array $routeParams = [], string $origin = 'default'): Response
    {
        $localeNames = Locales::getNames();
        $locales = [];
        foreach ($this->settings->get('app.languages') as $locale) {
            $locales[$locale] = $localeNames[$locale];
        }

        $categoryFilters = $this->em->getRepository(FilterCategory::class)->findAll();

        $defaultFilterStatus = [];
        foreach ($categoryFilters as $category) {
            $defaultFilterStatus[] = [
                'id' => $category->getId(),
                'selected' => [],
            ];
        }

        $routeStatus = [];

        return $this->render('admin/itinerary/app.html.twig', [
            'user' => $this->getUser(),
            'locales' => $locales,
            'defaultLocale' => $this->settings->get('app.defaultLanguage'),
            'userLocale' => $this->getUser()->getLocale(),
            'froalaKey' => $this->settings->get('app.froalaKey'),

            'activeRoute' => $activeRoute,
            'activeParams' => base64_encode(json_encode(array_merge($routeParams, ['origin' => $origin]))),
            'routeStatus' => base64_encode(json_encode($routeStatus)),
            'config' => base64_encode(json_encode([
                'defaultFilterStatus' => $defaultFilterStatus,
                'contentTitle' => 'ITINERARY.NEW_ITINERARY',
                'buttonSave2' => $this->translator->trans('Save and create other', [], 'messages', $this->getUser()->getLocale()),
            ])),
            'jwt' => $this->getJwt($this->JWTManager, $this->settings, $this->getUser()),
        ]);
    }

    /**
     * @Rest\Get("/admin/itineraries/available-tags")
     */
    public function getItinerariesTags(Request $request): Response
    {
        $query = $request->get('query', '');
        $result = $this->em->getRepository(Filter::class)->createQueryBuilder('f')
            ->select('f.id, f.name')
            ->where('f.name LIKE :query')
            ->setParameter('query', "%$query%")
            ->getQuery()
            ->getArrayResult();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $result,
        ]);
    }

    /**
     * @Rest\Post("/admin/itinerary/save", name="save_itinerary")
     */
    public function saveItinerary(Request $request): Response
    {
        $error = $this->validateRequest($request);
        if ('' !== $error) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $error,
            ]);
        }
        $id = $request->get('id');

        if (empty($id) || empty($itinerary = $this->em->getRepository(Itinerary::class)->find($id))) {
            $itinerary = new Itinerary();
            $sort = $this->em->getRepository(Itinerary::class)->getMaxSort();
            $itinerary->setSort($sort);
        }

        $tags = json_decode($request->get('tags'), false);
        $tagString = '';
        foreach ($tags as $index => $tag) {
            $tagString .= ($index ? ',' : '') . "[$tag]";
        }

        $itinerary
            ->setName($request->get('name'))
            ->setDescription($request->get('description'))
            ->setActive('true' === $request->get('active'))
            ->setTags($tagString);

        $this->em->persist($itinerary);
        $this->em->flush();

        $translations = json_decode($request->get('translations'), true);
        $this->saveItineraryTranslation($translations, $itinerary);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => ['id' => $itinerary->getId()],
        ]);
    }

    private function validateRequest($request): string
    {
        $id = $request->get('id');
        $name = $request->get('name');
        $description = $request->get('description');

        if (!is_numeric($id) || $id < 0) {
            return 'Invalid ID';
        }
        if (empty($name) || !\is_string($name) || \strlen("$name") < 3) {
            return 'Invalid Name';
        }

        if (!$this->isValidTagList($request->get('tags'))) {
            return 'Invalid Tags';
        }

        return '';
    }

    public function saveItineraryTranslation(array $translations, Itinerary $itinerary): void
    {
        try {
            foreach ($translations as $data) {
                $translation = $this->em->getRepository(ItineraryTranslation::class)->findOneBy([
                    'translatable' => $itinerary,
                    'locale' => $data['locale'],
                ]);

                $name = $data['name'] ?? null;
                $description = $data['description'] ?? null;

                if (empty($name) && empty($description)) {
                    if ($translation) {
                        $this->em->remove($translation);
                    }
                    continue;
                }

                if (!$translation) {
                    $translation = new ItineraryTranslation();
                    $translation->setTranslatable($itinerary);
                    $translation->setLocale($data['locale']);
                }

                $translation->setName($name)
                    ->setDescription($description);
                $this->em->persist($translation);
            }

            $this->em->flush();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function isValidTagList($value): bool
    {
        try {
            $tags = $value ? json_decode($value) : [];

            return \is_array($tags) || !empty($tags);
        } catch (\Exception $exception) {
            return false;
        }
    }

    /**
     * @Rest\Get("/admin/itinerary/tags")
     */
    public function getAvailableTags(): Response
    {
        $itineraries = $this->em->getRepository(Itinerary::class)->createQueryBuilder('i')
            ->select('i.tags as filter_id')
            ->where('i.tags is not null')
            ->getQuery()
            ->getResult() ?? [];

        $ids = [];
        foreach ($itineraries as $itinerary) {
            $ids = array_merge($ids, explode(',', str_replace(['[', ']'], '', $itinerary['filter_id'])));
        }
        $ids = array_unique($ids);

        $tags = [];
        foreach ($ids as $filterID) {
            $filter = $this->em->getRepository(Filter::class)->find($filterID);
            if ($filter) {
                $tags[] = [
                    'id' => $filter->getId(),
                    'name' => $filter->getName(),
                ];
            }
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $tags,
        ]);
    }

    /**
     * @Rest\Patch("/admin/itinerary/{id}/activate", requirements={"id"="\d+"})
     */
    public function activateItinerary(Request $request, Itinerary $itinerary): Response
    {
        $code = Response::HTTP_ACCEPTED;
        $content = json_decode($request->getContent(), true);

        if (isset($content['active'])) {
            $itinerary->setActive($content['active']);
            $this->em->persist($itinerary);
            $this->em->flush();

            $code = Response::HTTP_OK;
        }

        $this->notifyUsers($itinerary);

        return $this->sendResponse([
            'status' => $code,
            'error' => false,
        ]);
    }

    private function notifyUsers(Itinerary $itinerary): void
    {
        foreach ($itinerary->getItineraryUsers() as $itineraryUser) {
            $this->createNotificationItinerary($itineraryUser->getUser(), $itinerary);
        }

        foreach ($itinerary->getFilters() as $filter) {
            $users = $this->em->getRepository(User::class)->getUsersByFilter($filter);
            foreach ($users as $user) {
                $this->createNotificationItinerary($user, $itinerary);
            }
        }
    }

    /**
     * @Rest\Put("admin/itinerary/changeSort")
     */
    public function changeItinerarySort(Request $request)
    {
        $itineraries = json_decode($request->getContent(), true)['itineraries'];

        foreach ($itineraries as $itinerary) {
            $sortItinerary = $this->em->getRepository(Itinerary::class)->find($itinerary['id']);
            $sortItinerary->setSort($itinerary['newSort']);
            $this->em->persist($sortItinerary);
        }

        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => '',
        ]);
    }

    /**
     * @Rest\Post("admin/itinerary/cloneItinerary")
     */
    public function cloneItinerary(Request $request)
    {
        try {
            $itineraryId = json_decode($request->getContent(), true)['itineraryId'];
            $itinerary = $this->em->getRepository(Itinerary::class)->find($itineraryId);

            $itineraryClone = $this->cloneInitinerary($itinerary);
            $this->cloneItineraryCourses($itinerary, $itineraryClone);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => '',
            ]);
        } catch (\Exception $exception) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $exception,
            ]);
        }
    }

    private function cloneInitinerary(Itinerary $itinerary): Itinerary
    {
        $nextSort = $this->em->getRepository(Itinerary::class)->getMaxSort();

        $itineraryClone = new Itinerary();
        $itineraryClone->setName($itinerary->getName() . '-clone');
        $itineraryClone->setDescription($itinerary->getDescription());
        $itineraryClone->setTags($itinerary->getTags());
        $itineraryClone->setActive($itinerary->isActive());
        $itineraryClone->setSort($nextSort);
        $this->em->persist($itineraryClone);
        $this->em->flush();

        return $itineraryClone;
    }

    private function cloneItineraryCourses(Itinerary $itinerary, Itinerary $itineraryClone): void
    {
        $itineraryCourses = $this->em->getRepository(ItineraryCourse::class)->findBy(['itinerary' => $itinerary]);

        foreach ($itineraryCourses as $index => $itineraryCourse) {
            $course = $this->em->getRepository(Course::class)->find($itineraryCourse->getCourse()->getId());

            $newItineraryCourse = new ItineraryCourse();
            $newItineraryCourse->setItinerary($itineraryClone);
            $newItineraryCourse->setCourse($course);
            $newItineraryCourse->setPosition($index + 1);
            $this->em->persist($newItineraryCourse);
        }

        $this->em->flush();
    }

    /**
     * @Route("/admin/itinerary/{itinerary}/zip-export", name="itinerary-zip-export")
     */
    public function zipItineraryExport(Itinerary $itinerary, Request $request): Response
    {
        $content = json_decode($request->getContent(), true);
        if (!$content) {
            throw new \Exception('No se recibieron datos en la solicitud.');
        }

        $content['itineraryId'] = $itinerary->getId();
        $content['source'] = ['itinerary'];
        $fileName = '';
        $fileName .= $itinerary->getName();

        $this->zipFileTaskService->enqueueZipFileTask(
            $this->getUser(),
            'itinerary_perItineraryStatsReport',
            $content,
            StatsReportType::GENERAL_ITINERARY,
            $fileName . ' - ' . (new \DateTimeImmutable())->format('d-m-Y H:i:s'),
            $itinerary->getId()
        );

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'message' => 'Zip generado correctamente',
            ],
        ], ['groups' => 'itinerary']);
    }

    /**
     * @Rest\Get("/admin/itineraries_exports", name="dowload_all_itineraries")
     */
    public function dowloadExcelAllItineraries(Request $request, ExportRepository $exportRepository): Response
    {
        $content = json_decode($request->getContent(), true);
        $conditions['courseId'] = empty($content['courseId']) ? '' : $content['courseId'];
        $conditions['tags'] = empty($content['tags']) ? '' : $content['tags'];

        // convertir array de tags a string
        $conditions['tags'] = json_encode($conditions['tags']);

        $filename = $this->translator->trans('itinerary.label_in_plural', [], 'messages', $this->getUser()->getLocale());
        $dateFile = new \DateTimeImmutable();
        $filename = $filename . '_' . $dateFile->format('dmY');

        $result = $this->taskService->enqueueTask(
            $this->getUser(),
            'export-file',
            $conditions,
            'itinerary-all-stats',
            $filename
        );

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'message' => $this->translator->trans('stats.export.export_success', [], 'messages', $this->getUser()->getLocale()) .
                    '  ' . $this->translator->trans('stats.export.export_dir', [], 'messages', $this->getUser()->getLocale()),
        ]);
    }

    public function delete(AdminContext $context)
    {
        $entity = $context->getEntity();
        $itineraryRepository = $this->em->getRepository(Itinerary::class);
        $itinerary = $itineraryRepository->find($entity->getPrimaryKeyValue());

        try {
            $this->addFlash('success', $this->translator->trans('rouletteWord.response.delete_letter'));
            $responseParameters = parent::delete($context);

            return $this->redirect($this->adminUrlGenerator->setAction(Action::INDEX)->unset(EA::ENTITY_ID)->generateUrl());
        } catch (Exception $e) {
            $this->addFlash('danger', $this->translator->trans('itinerary.delete.error') . $e->getMessage());

            return $this->redirect(
                $this->adminUrlGenerator
                    ->setEntityId($itinerary->getCourse()->getId())
                    ->setController(ItineraryCrudController::class)
                    ->setAction(Action::DETAIL)->generateUrl()
            );
        }
    }
}
