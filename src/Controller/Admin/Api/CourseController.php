<?php

declare(strict_types=1);

namespace App\Controller\Admin\Api;

use App\Admin\Traits\SerializerTrait;
use App\Entity\Announcement;
use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\Season;
use App\Entity\TypeCourse;
use App\Entity\UrlShortener;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Repository\ChapterRepository;
use App\Service\Course\admin\CourseService;
use App\Service\Course\CloneRolePlayService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use FOS\RestBundle\Controller\Annotations as Rest;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @Route("/admin/api/v1")
 *
 * @Security("is_granted('ROLE_SUPER_ADMIN') or is_granted('ROLE_ADMIN') or is_granted('ROLE_MANAGER') or is_granted('ROLE_CREATOR')")
 */
class CourseController extends AbstractController
{
    use SerializerTrait;

    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly SettingsService $settings,
        private readonly CourseService $courseService,
        private readonly CloneRolePlayService $cloneRolePlayService,
        private readonly AdminUrlGenerator $adminUrlGenerator,
        private readonly TranslatorInterface $translator
    ) {
    }

    /**
     * @Rest\Get("/courses")
     */
    public function getAllCourses(Request $request): Response
    {
        $content = $this->courseService->getParameters($request);

        /** @var User $user */
        $user = $this->getUser();
        $locale = $user->getLocale() ?? $this->settings->get('app.adminDefaultLanguage');

        $data = [
            'courses' => $this->courseService->getAllCourse($content, $user),
            'totalCourses' => $this->courseService->getTotalCourses($content, $user),
            'typeCourses' => $this->courseService->getAllTypeCoursesByLocale($locale),
            'courseCategories' => $this->courseService->getAllCourseCategoriesByLocale($locale),
            'creatorsCourses' => $this->courseService->getAllCreatorsCourses(),
        ];

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Rest\Get("/courses/{id}")
     */
    public function getCourseInfo(Course $course): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $locale = $user->getLocale() ?? $this->settings->get('app.adminDefaultLanguage');
        $data = $this->courseService->getCoursedata($course, $locale);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Rest\Get("/courses/{id}/translates")
     */
    public function getCourseTranslateInfo(Course $course): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $this->courseService->getCourseTranslatesData($course),
        ]);
    }

    /**
     * @Rest\POST("/courses/{id}/chapters")
     */
    public function getCourseChapters(Request $request, Course $course): Response
    {
        $currentUrl = $request->getUri();
        $urlData = json_decode($request->getContent(), true);
        $combinedUrl = $currentUrl . 'courseUrlRedirectNew' . $urlData['url'];

        $addChapterUrl = $this->adminUrlGenerator
            ->unsetAll()
            ->setController('App\\Controller\\Admin\\ChapterCrudController')
            ->setAction('new')
            ->set('courseId', $course->getId())
            ->set('referrer', $combinedUrl)
            ->generateUrl();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'courseChapters' => $this->courseService->getChapters($course, $urlData['url']),
                'addChapterUrl' => $addChapterUrl,
            ],
        ]);
    }

    /**
     * @Rest\DELETE("/courses/chapters/{id}")
     */
    public function deleteCourseChapter(Chapter $chapter): Response
    {
        try {
            $userCourseChapters = $this->em->getRepository(UserCourseChapter::class)->findBy(['chapter' => $chapter]);
            if ($userCourseChapters) {
                $chapter->setIsActive(false);
                $this->em->persist($chapter);
            } else {
                $this->em->remove($chapter);
            }
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => $e,
                'error' => true,
            ]);
        }
    }

    /**
     * @Rest\Get("/courses/{id}/seasons")
     */
    public function getCourseSeasons(Course $course): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $this->courseService->getSeason($course),
        ]);
    }

    /**
     * @Rest\POST("/courses/{id}/seasons")
     */
    public function createCourseSeasons(Request $request, Course $course): Response
    {
        try {
            if (!$this->isGranted('COURSE_CREATE_SEASON', $course)) {
                return $this->sendResponse([
                    'status' => Response::HTTP_FORBIDDEN,
                    'error' => true,
                    'message' => 'You do not have permission to create seasons for this course.',
                ]);
            }

            $this->courseService->createCourseSeasons($request, $course);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => $e,
                'error' => true,
            ]);
        }
    }

    /**
     * @Rest\PUT("/courses/seasons/{id}")
     */
    public function editCourseSeasons(Request $request, Season $season): Response
    {
        try {
            if (!$this->isGranted('COURSE_EDIT_SEASON', $season->getCourse())) {
                return $this->sendResponse([
                    'status' => Response::HTTP_FORBIDDEN,
                    'error' => true,
                    'message' => 'You do not have permission to edit seasons for this course.',
                ]);
            }

            $content = json_decode($request->getContent(), true);
            $this->courseService->editCourseSeasons($season, $content);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => $e,
                'error' => true,
            ]);
        }
    }

    /**
     * @Rest\DELETE("/courses/seasons/{id}")
     */
    public function removeCourseSeasons(Season $season, ChapterRepository $chapterRepository): Response
    {
        try {
            if (!$this->isGranted('COURSE_DELETE_SEASON', $season->getCourse())) {
                return $this->sendResponse([
                    'status' => Response::HTTP_FORBIDDEN,
                    'error' => true,
                    'message' => 'You do not have permission to delete seasons for this course.',
                ]);
            }

            $chapters = $chapterRepository->findBy(['isActive' => true, 'deletedAt' => null, 'season' => $season]);
            if (\count($chapters) > 0) {
                return $this->sendResponse([
                    'status' => Response::HTTP_OK,
                    'error' => true,
                    'data' => \count($chapters),
                    'message' => $this->translator->trans(
                        'season.delete',
                        [],
                        'messages',
                        $this->getUser()->getLocale()
                    ),
                ]);
            }

            $this->em->remove($season);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => $e,
                'error' => true,
            ]);
        }
    }

    /**
     * @Rest\PUT("/courses/{id}/active")
     */
    public function updateCourseActive(Course $course): Response
    {
        $contentCompleted = true;
        if (TypeCourse::TYPE_PRESENCIAL != $course->getTypeCourse()->getId() && !$course->getActive()) {
            if ($course->getChapters()->count() > 0) {
                foreach ($course->getChapters() as $chapter) {
                    if (!$chapter->hasContentCompleted()) {
                        $contentCompleted = false;
                        break;
                    }
                }
            }
            if (0 === $course->getChapters()->count() || !$contentCompleted) {
                return $this->sendResponse([
                    'status' => Response::HTTP_OK,
                    'error' => true,
                    'message' => $this->translator->trans('course.publish.message.unactive.chapters', [], 'messages', $this->getUser()->getLocale()),
                ]);
            }
        }
        $course->setActive(!$course->getActive());
        $this->em->persist($course);
        $this->em->flush();
        $message = $this->translator->trans($course->getActive() ? 'course.publish.message.active' : 'course.publish.message.unactive', [], 'messages', $this->getUser()->getLocale());

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'message' => $message,
        ]);
    }

    /**
     * @Rest\Delete("/courses/{id}")
     */
    public function deleteCourse(Course $course): Response
    {
        if (!$this->isGranted('COURSE_DELETE', $course)) {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => true,
                'message' => "You can't hare permission to delete this course",
            ]);
        }

        $user = $this->getUser();
        $usersCourse = $this->em->getRepository(UserCourse::class)->findBy(['course' => $course]);
        $isUserInCourse = false;
        if ($usersCourse) {
            foreach ($usersCourse as $userCourse) {
                if ($userCourse->getUser() === $user) {
                    $isUserInCourse = true;
                    break;
                }
            }
        }
        $testCourseData = $isUserInCourse && 1 == \count($usersCourse);
        $isCourseInAnnouncement = $course->getAnnouncements()->count() > 0;
        $canDelete = false;
        if (\in_array('ROLE_CREATOR', $user->getRoles())) {
            if ($user == $course->getCreatedBy() && ($testCourseData || !$usersCourse) && !$isCourseInAnnouncement) {
                $canDelete = true;
            }
        }
        if ((\in_array('ROLE_ADMIN', $user->getRoles()) || \in_array('ROLE_SUPER_ADMIN', $user->getRoles())) && ($testCourseData || !$usersCourse) && !$isCourseInAnnouncement) {
            $canDelete = true;
        }

        if ($canDelete) {
            $this->em->remove($course);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
            ]);
        } else {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => true,
                'message' => $this->translator->trans('course.undelete.message', [], 'messages', $this->getUser()->getLocale()),
            ]);
        }
    }

    /**
     * @Rest\POST("/courses/{id}/clone")
     */
    public function cloneCourse(Course $course): Response
    {
        $clonedCourse = clone $course;
        $clonedCourse->setName($clonedCourse->getName() . '[Clon]');
        $clonedCourse->setCreatedAt(new \DateTime());
        $clonedCourse->setUpdatedAt(new \DateTime());
        $this->cloneRolePlayService->cloneRolePlay($clonedCourse);
        $this->em->persist($clonedCourse);

        $translateCourses = $this->em->getRepository(Course::class)->findBy(['translation' => $course]);
        foreach ($translateCourses as $course) {
            $translateCloneCourse = clone $course;
            $translateCloneCourse->setTranslation($clonedCourse);
            $translateCloneCourse->setName($course->getName() . '[Clon]');
            $translateCloneCourse->setCreatedAt(new \DateTime());
            $translateCloneCourse->setUpdatedAt(new \DateTime());
            $this->em->persist($translateCloneCourse);
        }

        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
        ]);
    }

    /**
     * @Rest\GET("/courses/{id}/share")
     */
    public function shareCourseUrl(Request $request, Course $course): Response
    {
        /** @var UrlShortenerRepository $urlShortenerRepository */
        $urlShortenerRepository = $this->em->getRepository(UrlShortener::class);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $urlShortenerRepository->getShortUrl($request, '/campus/course/' . $course->getId()),
        ]);
    }

    /**
     * @Rest\Get("/courses/{id}/announcements")
     */
    public function getCourseAnnouncements(Course $course): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $this->courseService->getCourseAnnouncements($course),
        ]);
    }

    /**
     * @Rest\Post("/courses/{courseId}/translate")
     */
    public function translateCourse(Request $request, int $courseId): Response
    {
        $content = json_decode($request->getContent(), true);

        $locale = $content['language'] ?? 'pt';

        $original = $this->em->getRepository(Course::class)->find($courseId);

        if (!$original) {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => true,
                'message' => \sprintf('Course with ID=%d not found.', $courseId),
            ]);
        }

        $courseTranslation = $this->em->getRepository(Course::class)->findOneBy(['translation' => $original, 'locale' => $locale]);

        if ($courseTranslation) {
            return $this->sendResponse([
                'status' => Response::HTTP_CONFLICT,
                'error' => true,
                'message' => \sprintf('Translation for course ID=%d already exists in locale "%s".', $courseId, $locale),
            ]);
        }

        $translated = clone $original;
        $translated->setLocale($locale);
        $translated->setTranslation($original);

        $this->em->persist($translated);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'message' => 'Translation created successfully',
            'data' => [
                'original_course_id' => $original->getId(),
                'translated_course_id' => $translated->getId(),
                'locale' => $locale,
            ],
        ]);
    }

    /**
     * @Rest\DELETE("/announcement/{id}")
     */
    public function deleteannouncement(Announcement $announcement): Response
    {
        try {
            $this->em->remove($announcement);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => $e,
                'error' => true,
            ]);
        }
    }

    /**
     * @Route("/courses/{id}/chapters/order", name="courses-chapter-order", methods={"PUT"})
     */
    public function coursesChaptersOrder(Course $course, Request $request)
    {
        try {
            $content = json_decode($request->getContent(), true);
            $ordered_items = $content['order'] ?? [];
            $seasonId = $content['season'] ?? null;
            $positions = [];

            if (empty($ordered_items) || !\is_array($ordered_items)) {
                throw new \InvalidArgumentException('CHAPTER.INVALID_FORMAT');
            }

            if (empty($seasonId)) {
                throw new \InvalidArgumentException('CHAPTER.INVALID_SEASON');
            }

            $season = $this->em->getRepository(Season::class)->find($seasonId);
            $chapterRepository = $this->em->getRepository(Chapter::class);

            $chaptersCourse = $chapterRepository->getChaptersByCourseSeason($course, $season, $ordered_items);

            if (!$chaptersCourse) {
                throw new \InvalidArgumentException('CHAPTER.INVALID_CHAPTERS');
            }

            foreach ($ordered_items as $index => $item) {
                $chapter = $chapterRepository->find($item);

                if (!$chapter) {
                    throw new \Exception("Item with ID {$item} not found");
                }

                $chapterSeason = $chapterRepository->getChaptersBySeason($season, $chapter);
                if (!$chapterSeason) {
                    throw new \Exception('CHAPTER.SEASON_ERROR');
                }

                $chapter->setPosition($index + 1);

                $positions[] = [
                    'id' => $chapter->getId(),
                    'name' => $chapter->getTitle(),
                    'position' => $index + 1,
                    'newPosition' => $chapter->getPosition(),
                ];
            }

            $this->em->flush();

            return $this->sendResponse([
                'status' => 200,
                'error' => false,
                'data' => 'CHAPTER.SUCCESS',
                'order' => $positions,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => 500,
                'error' => true,
                'data' => 'Error: ' . $e->getMessage(),
            ]);
        }
    }
}
