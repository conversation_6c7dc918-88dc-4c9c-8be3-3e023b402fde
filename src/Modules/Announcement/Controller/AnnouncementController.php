<?php

declare(strict_types=1);

namespace App\Modules\Announcement\Controller;

use App\Admin\Traits\SerializerTrait;
use App\Entity\Announcement;
use App\Entity\AnnouncementManager;
use App\Entity\Export;
use App\Entity\User;
use App\Entity\ZipFileTask;
use App\Service\Annoucement\Admin\AnnouncementCloneService;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\Annoucement\Admin\AnnouncementNotificationService;
use App\Service\Annoucement\Email\AnnouncementEmailService;
use App\Service\Annoucement\Report\AnnouncementReportService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\UnexpectedResultException;
use FOS\RestBundle\Controller\Annotations as Rest;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class AnnouncementController extends AbstractController
{
    use SerializerTrait;

    private EntityManagerInterface $em;
    private AnnouncementNotificationService $announcementNotificationService;
    private AnnouncementEmailService $announcementEmailService;
    private AnnouncementCloneService $announcementCloneService;
    private AnnouncementConfigurationsService $announcementConfigurationsService;

    public function __construct(
        EntityManagerInterface $em,
        AnnouncementNotificationService $announcementNotificationService,
        AnnouncementEmailService $announcementEmailService,
        AnnouncementCloneService $announcementCloneService,
        AnnouncementConfigurationsService $announcementConfigurationsService
    ) {
        $this->em = $em;
        $this->announcementNotificationService = $announcementNotificationService;
        $this->announcementEmailService = $announcementEmailService;
        $this->announcementCloneService = $announcementCloneService;
        $this->announcementConfigurationsService = $announcementConfigurationsService;
    }

    /**
     * @Rest\Delete("/admin/announcement/{id}", requirements={"id"="\d+"}, name="admin_api_delete_announcement")
     *
     * @IsGranted("ROLE_MANAGER")
     */
    public function deleteAnnouncement(Announcement $announcement): Response
    {
        /** @var User $user */
        $user = $this->getUser();

        if (!$this->canUserDeleteAnnouncement($user, $announcement)) {
            return $this->sendResponse([
                'status' => Response::HTTP_FORBIDDEN,
                'error' => true,
                'data' => 'You do not have permission to delete this announcement.',
            ]);
        }

        try {
            $announcement->setStatus(Announcement::STATUS_ARCHIVED);
            $this->em->persist($announcement);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_NO_CONTENT,
                'error' => false,
                'data' => 'ANNOUNCEMENT.DELETE.STATUS_ARCHIVED',
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'Error to delete announcement {' . $e->getMessage() . '}',
            ]);
        }
    }

    private function canUserDeleteAnnouncement(User $user, Announcement $announcement): bool
    {
        if ($user->isAdmin()) {
            return true;
        }

        if (
            $announcement->getCreatedBy()->getId() === $user->getId()
            && Announcement::canChangeStatus($announcement, Announcement::STATUS_ARCHIVED)
        ) {
            return true;
        }

        return false;
    }

    /**
     * @Rest\Delete("/admin/announcement/{id}/soft", requirements={"id"="\d+"}, name="admin_api_soft_delete_announcement")
     *
     * @IsGranted("ROLE_ADMIN")
     */
    public function softDeleteAnnouncement(Announcement $announcement): Response
    {
        if (!$this->canSoftDelete($announcement)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'ANNOUNCEMENT.CHANGE_STATUS.DELETE_NOT_ALLOWED',
            ]);
        }

        try {
            $this->em->remove($announcement);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_NO_CONTENT,
                'error' => false,
                'data' => 'ANNOUNCEMENT.DELETE.SUCCESS_DELETE',
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'Error to delete announcement {' . $e->getMessage() . '}',
            ]);
        }
    }

    private function canSoftDelete(Announcement $announcement): bool
    {
        return Announcement::STATUS_ARCHIVED === $announcement->getStatus();
    }

    private function validateAnnouncementUser(Announcement $announcement): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        if (
            !$user->isAdmin() && ($announcement->getCreatedBy()->getId() !== $user->getId()
                || Announcement::STATUS_ACTIVE !== $announcement->getStatus())
        ) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'ANNOUNCEMENT.CHANGE_STATUS.NOT_ALLOWED',
            ]);
        }

        return true;
    }

    private function validatorAnnouncementStartAtFinishAt(Announcement $announcement): Response
    {
        $tz = new \DateTimeZone($announcement->getTimezoneOrDefault());
        $current = new \DateTimeImmutable('now', $tz);
        if ($current >= $announcement->getStartAt() && $current <= $announcement->getFinishAt()) { // Is in progress, cannot deactivate
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'ANNOUNCEMENT.CHANGE_STATUS.NOT_ALLOWED',
            ]);
        }

        return true;
    }

    /**
     * @Rest\Delete("/admin/announcement/{id}/deactivate", requirements={"id"="\d+"}, name="admin_api_deactivate_announcement")
     *
     * @IsGranted("ROLE_MANAGER")
     *
     * @throws \Exception
     */
    public function deactivateAnnouncement(Announcement $announcement): Response
    {
        if (($result = $this->validateAnnouncementUser($announcement)) instanceof Response) {
            return $result;
        }

        if (($result = $this->validatorAnnouncementStartAtFinishAt($announcement)) instanceof Response) {
            return $result;
        }

        $announcement->setStatus(Announcement::STATUS_INACTIVE);
        $this->em->persist($announcement);
        $this->em->flush();

        // Enviar email de desactivación
        if ($this->announcementConfigurationsService->hasEmailNotificationOnAnnouncement()) {
            $this->announcementEmailService->desactiveEventAannouncement($announcement);
        }

        // crear notificacion para la plataforma
        if ($this->announcementConfigurationsService->hasNotificationOnAnnouncement()) {
            $this->announcementNotificationService->createNoticationCancelationEventAnnoucement($announcement);
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => 'ANNOUNCEMENT.STATUS_INACTIVE',
        ]);
    }

    /**
     * @Rest\Post("/admin/clone-announcement/{id}", requirements={"id"="\d+"}, name="admin_clone_announcement")
     */
    public function cloneAnnouncement(Announcement $announcement): Response
    {
        return $this->sendResponse($this->announcementCloneService->cloneAnnouncement($announcement));
    }

    /**
     * @Rest\Get("/admin/announcement/reports")
     */
    public function getAnnouncementReports(Request $request): Response
    {
        $page = $request->get('page');
        $pageSize = $request->get('pageSize', 10);
        $query = $this->em->getRepository(ZipFileTask::class)
            ->createQueryBuilder('ztf')
            ->leftJoin('ztf.filesManager', 'filesManager')
            ->join('ztf.createdBy', 'u')
            ->where('ztf.type = :type OR ztf.type = :type2')
            ->setParameters([
                'type' => AnnouncementReportService::TYPE_GROUP,
                'type2' => AnnouncementReportService::TYPE_ANNOUNCEMENT,
            ])
            ->orderBy('ztf.createdAt', 'DESC')
        ;

        /** @var User $user */
        $user = $this->getUser();
        if (!$user->isSuperAdmin() && !$user->isAdmin() && !$user->isManager()) {
            $query->andWhere('ztf.createdBy = :user')
                ->setParameter('user', $user)
            ;
        }

        $countQuery = clone $query;

        $reportsQuery = $query
            ->select('ztf.entityId', 'ztf.id as taskId', 'ztf.status', 'ztf.params', 'ztf.createdAt', 'ztf.finishedAt', 'ztf.type')
            ->addSelect('filesManager.filename', 'IFNULL(filesManager.originalName, ztf.originalName) as originalName', 'filesManager.mimeType')
            ->addSelect('CONCAT(u.firstName, \' \', u.lastName) as createdByName');

        $data = [];

        if (null != $page && $page > 0) {
            $pageSize = (int) $pageSize;
            $reports = $reportsQuery
                ->setMaxResults($pageSize)
                ->setFirstResult(($page - 1) * $pageSize)
                ->getQuery()
                ->getResult();
        } else {
            $reports = $reportsQuery->getQuery()->getResult();
        }

        foreach ($reports as &$r) {
            if (AnnouncementReportService::TYPE_GROUP === $r['type']) {
                $announcement = $this->em->getRepository(Announcement::class)
                    ->createQueryBuilder('a')
                    ->select('a.id', 'a.code')
                    ->addSelect('tc.denomination as source')
                    ->join('a.announcementGroups', 'ag')
                    ->join('a.course', 'c')
                    ->join('c.typeCourse', 'tc')
                    ->where('ag.id = :groupId')
                    ->setParameter('groupId', $r['entityId'])
                    ->getQuery()
                    ->getOneOrNullResult();
            } else {
                $announcement = $this->em->getRepository(Announcement::class)->createQueryBuilder('a')
                    ->select('a.id', 'a.code')
                    ->addSelect('tc.denomination as source')
                    ->join('a.course', 'c')
                    ->join('c.typeCourse', 'tc')
                    ->where('a.id = :id')
                    ->setParameter('id', $r['entityId'])
                    ->getQuery()
                    ->getOneOrNullResult();
            }

            if (null === $announcement) {
                $r['announcementId'] = null;
                $r['announcementName'] = null;
                $r['source'] = null;
            } else {
                $r['announcementId'] = $announcement['id'];
                $r['announcementName'] = $announcement['code'];
                $r['source'] = $announcement['source'];
            }
        }

        if (null != $page && $page > 0) {
            try {
                $totalItems = $countQuery
                    ->select('COUNT(DISTINCT ztf.id)')
                    ->getQuery()
                    ->getSingleScalarResult();
            } catch (UnexpectedResultException $e) {
                $totalItems = 0;
            }

            $data = [
                'items' => $reports,
                'totalItems' => (int) $totalItems,
            ];
        } else {
            $data = $reports;
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Rest\Get("/admin/announcement/reports/participants")
     */
    public function getAnnouncementParticipantReports(Request $request): Response
    {
        $page = $request->get('page');
        $pageSize = $request->get('pageSize', 10);

        $exportRepository = $this->em->getRepository(Export::class);

        $query = $exportRepository->getAnnouncementParticipantReports();

        $countQuery = clone $query;

        $reportsQuery = $query->addSelect([
            'e.id',
            'e.created_at',
            'e.available_until',
            'e.filename',
            'e.meta',
            'e.finished_at',
            't.status'
        ])
        ->orderBy('e.id', 'DESC');

        $data = [];

        if (null != $page && $page > 0) {
            $pageSize = (int) $pageSize;
            $reports = $reportsQuery
                ->setMaxResults($pageSize)
                ->setFirstResult(($page - 1) * $pageSize)
                ->getQuery()
                ->getResult();
        } else {
            $reports = $reportsQuery->getQuery()->getResult();
        }

        if (null != $page && $page > 0) {
            try {
                $totalItems = $countQuery
                    ->select('COUNT(DISTINCT e.id)')
                    ->getQuery()
                    ->getSingleScalarResult();
            } catch (UnexpectedResultException $e) {
                $totalItems = 0;
            }

            $data = [
                'items' => $reports,
                'totalItems' => (int) $totalItems,
            ];
        } else {
            $data = $reports;
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Rest\Post("/api/admin/announcement/{id}/managers")
     *
     * @IsGranted("ROLE_MANAGER")
     */
    public function setAnnouncementManagers(Announcement $announcement, Request $request): Response
    {
        /** @var User $user */
        $user = $this->getUser();

        // Check if user is admin or the creator of the announcement
        if (!$user->isAdmin() && $announcement->getCreatedBy()->getId() !== $user->getId()) {
            return $this->sendResponse([
                'status' => Response::HTTP_FORBIDDEN,
                'error' => true,
                'data' => 'You do not have permission to modify this announcement.',
            ]);
        }

        // Get and validate payload
        $data = json_decode($request->getContent(), true);

        if (!isset($data['managers']) || !\is_array($data['managers'])) {
            return $this->sendResponse([
                'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
                'error' => true,
                'data' => 'Invalid payload. The "managers" field must be an array of integers.',
            ]);
        }

        // Validate that all IDs are integers
        foreach ($data['managers'] as $managerId) {
            if (!\is_int($managerId)) {
                return $this->sendResponse([
                    'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
                    'error' => true,
                    'data' => 'Invalid payload. All manager IDs must be integers.',
                ]);
            }
        }

        // Validate that all IDs correspond to actual managers
        $userRepository = $this->em->getRepository(User::class);
        $validManagerIds = [];

        foreach ($data['managers'] as $managerId) {
            $manager = $userRepository->find($managerId);

            if (!$manager || (!$manager->isManager() && !$manager->isAdmin())) {
                return $this->sendResponse([
                    'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
                    'error' => true,
                    'data' => 'Invalid manager ID: ' . $managerId,
                ]);
            }

            $validManagerIds[] = $managerId;
        }

        try {
            // Remove all existing announcement managers
            $announcementManagerRepository = $this->em->getRepository(AnnouncementManager::class);
            $announcementManagerRepository->removeAllByAnnouncement($announcement);

            // Add new announcement managers
            foreach ($validManagerIds as $managerId) {
                $manager = $userRepository->find($managerId);
                $announcementManager = new AnnouncementManager();
                $announcementManager->setAnnouncement($announcement);
                $announcementManager->setManager($manager);
                $this->em->persist($announcementManager);
            }

            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_NO_CONTENT,
                'error' => false,
                'data' => null,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'Error updating announcement managers: ' . $e->getMessage(),
            ]);
        }
    }
}
