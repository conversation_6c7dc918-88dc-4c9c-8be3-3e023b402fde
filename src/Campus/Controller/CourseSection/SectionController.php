<?php


namespace App\Campus\Controller\CourseSection;

use App\Campus\Controller\Base\BaseController;
use App\Campus\Service\CourseSection\SectionDetailService;
use App\Campus\Service\CourseSection\SectionsService;
use App\Entity\CourseSection;
use App\Repository\CourseSectionRepository;
use App\Serializer\Normalizer\CourseSectionNormalizer;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;


/**
 * @Route("/api")
 */
class SectionController extends BaseController
{
    private SectionDetailService $sectionDetailService;
    private SectionsService $sectionsService;


    /**
     * ApiController constructor.
     *
     * @param $logger
     */
    public function __construct(
        SettingsService $settings,
        EntityManagerInterface $em,
        TranslatorInterface $translator,     
        SectionDetailService $sectionDetailService,
        SectionsService $sectionsService

    ) {

        parent::__construct($settings, $em, $translator);

        $this->sectionDetailService = $sectionDetailService;
        $this->sectionsService = $sectionsService;
    }

    /**
     * @Rest\Get("/sections", name="api_course_sections_list-user")
     *
     * @param CourseSectionRepository $courseSectionRepository
     * @param CourseSectionNormalizer $courseSectionNormalizer
     * @return Response
     */
    public function courseSectionsList(CourseSectionNormalizer $courseSectionNormalizer, Request $request): Response
    {
        return $this->executeSafe(function () use ($courseSectionNormalizer, $request) {
                      $sections = $this->sectionsService->getCoursesSectionForCampus();

            $data = [
                'sections' => $sections,
                'training' => []

            ];

            $this->normalizers[] = $courseSectionNormalizer;

            return $data;
        }, [], ['course_section:index']);
    }


    /**
     * @Route("/sections/{id}", name="api_course_sections_detail-new")
     * @Route("/sections/{slug}", name="api_course_sections_detail_slug-new")
     */
    public function courseSectionDetail(CourseSection $courseSection): Response
    {
        return $this->executeSafe(function () use ($courseSection) {
            return $this->sectionDetailService->getCourseSectionDetail($courseSection);
        }, [], ['list']);
    }
}
