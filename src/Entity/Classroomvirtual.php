<?php

namespace App\Entity;

use App\Repository\ClassroomvirtualRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=ClassroomvirtualRepository::class)
 */
class Classroomvirtual
{
    public const MINIMUM_ATTENDANCE_TIME = 0.8;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"classroomvirtual"})
     */
    private $id;

    /**
     * @ORM\Column(type="bigint")
     * @Groups({"classroomvirtual"})
     */
    private $roomid;

    /**
     * @ORM\Column(type="integer")
     * @Groups({"classroomvirtual"})
     */
    private $roomtype;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"classroomvirtual"})
     */
    private $name;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Groups({"classroomvirtual"})
     */
    private $description;

    /**
     * @ORM\Column(type="datetime")
     * @Groups({"classroomvirtual"})
     */
    private $startsat;

    /**
     * @ORM\Column(type="integer")
     * @Groups({"classroomvirtual"})
     */
    private $duration;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Groups({"classroomvirtual"})
     */
    private $sessionNumber;

    /**
     * @ORM\Column(type="string", length=50)
     * @Groups({"classroomvirtual"})
     */
    private $state;

    /**
     * @ORM\ManyToOne(targetEntity=AnnouncementGroup::class, inversedBy="classroomvirtuals")
     * @Groups({"classroomvirtual"})
     */
    private $announcementgroup;

    /**
     * @ORM\ManyToOne(targetEntity=ClassroomvirtualType::class, inversedBy="classroomvirtuals")
     * @Groups({"classroomvirtual"})
     */
    private $classroomvirtualType;

    /**
     * @ORM\OneToMany(targetEntity=ClassroomvirtualUser::class, mappedBy="classroomvirtual")
     */
    private $classroomvirtualUsers;

    /**
     * @ORM\OneToOne(targetEntity=AnnouncementGroupSession::class, inversedBy="classroomvirtual", cascade={"persist"})
     */
    private $groupSession;

    /**
     * @ORM\OneToOne(targetEntity=ClassroomVirtualResult::class, mappedBy="classroomVirtual", cascade={"persist", "remove"})
     */
    private $classroomVirtualResult;

    public function __construct()
    {
        $this->classroomvirtualUsers = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getRoomid(): ?string
    {
        return $this->roomid;
    }

    public function setRoomid(string $roomid): self
    {
        $this->roomid = $roomid;

        return $this;
    }

    public function getRoomtype(): ?int
    {
        return $this->roomtype;
    }

    public function setRoomtype(int $roomtype): self
    {
        $this->roomtype = $roomtype;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getStartsat(): ?\DateTimeInterface
    {
        return $this->startsat;
    }

    public function setStartsat(\DateTimeInterface $startsat): self
    {
        $this->startsat = $startsat;

        return $this;
    }

    public function getDuration(): ?int
    {
        return $this->duration;
    }

    public function setDuration(int $duration): self
    {
        $this->duration = $duration;

        return $this;
    }

    public function getSessionNumber(): ?int
    {
        return $this->sessionNumber;
    }

    public function setSessionNumber(?int $sessionNumber): self
    {
        $this->sessionNumber = $sessionNumber;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getAnnouncementgroup(): ?AnnouncementGroup
    {
        return $this->announcementgroup;
    }

    public function setAnnouncementgroup(?AnnouncementGroup $announcementgroup): self
    {
        $this->announcementgroup = $announcementgroup;

        return $this;
    }

    public function getClassroomvirtualType(): ?ClassroomvirtualType
    {
        return $this->classroomvirtualType;
    }

    public function setClassroomvirtualType(?ClassroomvirtualType $classroomvirtualType): self
    {
        $this->classroomvirtualType = $classroomvirtualType;

        return $this;
    }

    /**
     * @return Collection<int, ClassroomvirtualUser>
     */
    public function getClassroomvirtualUsers(): Collection
    {
        return $this->classroomvirtualUsers;
    }

    public function addClassroomvirtualUser(ClassroomvirtualUser $classroomvirtualUser): self
    {
        if (!$this->classroomvirtualUsers->contains($classroomvirtualUser)) {
            $this->classroomvirtualUsers[] = $classroomvirtualUser;
            $classroomvirtualUser->setClassroomvirtual($this);
        }

        return $this;
    }

    public function removeClassroomvirtualUser(ClassroomvirtualUser $classroomvirtualUser): self
    {
        if ($this->classroomvirtualUsers->removeElement($classroomvirtualUser)) {
            // set the owning side to null (unless already changed)
            if ($classroomvirtualUser->getClassroomvirtual() === $this) {
                $classroomvirtualUser->setClassroomvirtual(null);
            }
        }

        return $this;
    }

    public function getGroupSession(): ?AnnouncementGroupSession
    {
        return $this->groupSession;
    }

    public function setGroupSession(?AnnouncementGroupSession $groupSession): self
    {
        $this->groupSession = $groupSession;

        return $this;
    }

    public function getClassroomVirtualResult(): ?ClassroomVirtualResult
    {
        return $this->classroomVirtualResult;
    }

    public function setClassroomVirtualResult(?ClassroomVirtualResult $classroomVirtualResult): self
    {
        // unset the owning side of the relation if necessary
        if ($classroomVirtualResult === null && $this->classroomVirtualResult !== null) {
            $this->classroomVirtualResult->setClassroomVirtual(null);
        }

        // set the owning side of the relation if necessary
        if ($classroomVirtualResult !== null && $classroomVirtualResult->getClassroomVirtual() !== $this) {
            $classroomVirtualResult->setClassroomVirtual($this);
        }

        $this->classroomVirtualResult = $classroomVirtualResult;

        return $this;
    }  
    
}
