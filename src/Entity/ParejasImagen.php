<?php

namespace App\Entity;

use App\Repository\ParejasImagenRepository;
use Symfony\Component\Serializer\Annotation\Groups;
use Doctrine\ORM\Mapping as ORM;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=ParejasImagenRepository::class)
 * @Vich\Uploadable()
 */
class ParejasImagen
{
    use \App\Behavior\Imageable;
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"parejas"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"parejas"})
     */
    private $texto;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups({"parejas"})
     */
    private $image;

    /**
     * @Vich\UploadableField(mapping="image_game_parejas", fileNameProperty="imagen")
     */
    private $imageFile;

    /**
     * @ORM\ManyToOne(targetEntity=parejas::class, inversedBy="parejasImagens")
     */
    private $parejas;


    public function __clone()
    {

        // clone image
        if ($this->getUploadsFolder()) {
            $this->cloneImage();
        }
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTexto(): ?string
    {
        return $this->texto;
    }

    public function setTexto(string $texto): self
    {
        $this->texto = $texto;

        return $this;
    }

    public function getImagen(): ?string
    {
        return $this->image;
    }

    public function setImagen(?string $imagen): self
    {
        $this->image = $imagen;

        return $this;
    }

    public function getParejas(): ?parejas
    {
        return $this->parejas;
    }

    public function setParejas(?parejas $parejas): self
    {
        $this->parejas = $parejas;

        return $this;
    }

    /**
     * @param mixed $imageFile
     * @return mixed
     */
    public function setImageFile($imageFile): self
    {
        $this->imageFile = $imageFile;
        return $this;
    }

    public function getImageFile()
    {
        return $this->imageFile;
    }
}
