<?php

namespace App\Entity;

use App\Repository\FillgapsRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=FillgapsRepository::class)
 */
class Fillgaps
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"roulette","fillgaps"})
     */
    private $id;

    /**
     * @ORM\Column(type="text")
     * @Groups({"roulette","fillgaps"})
     */
    private $text;

    /**
     * @ORM\Column(type="text")
     * @Groups({"roulette","fillgaps"})
     */
    private $answers;

    /**
     * @ORM\Column(type="integer")
     * @Groups({"roulette","fillgaps"})
     */
    private $time;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"roulette","fillgaps"})
     */
    private $label;

    /**
     * @ORM\ManyToOne(targetEntity=chapter::class, inversedBy="fillgaps")
     */
    private $chapter;

    /**
     * @ORM\OneToMany(targetEntity=Holes::class, mappedBy="fillgap", orphanRemoval=true, cascade={"persist"})
     */
    private $holes;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Groups({"fillgaps"})
     */
    private $extra;

    public function __construct()
    {
        $this->holes = new ArrayCollection();
    }

    public function __clone()
    {
        $this->id = null;

        $holes = $this->holes;
        $this->holes = new ArrayCollection();
        foreach ($holes as $hole) {
            $newHole = clone $hole;
            $this->addHole($newHole);
        }
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getText(): ?string
    {
        return $this->text;
    }

    public function setText(string $text): self
    {
        $this->text = $text;

        return $this;
    }

    public function getAnswers(): ?string
    {
        return $this->answers;
    }

    public function setAnswers(string $answers): self
    {
        $this->answers = $answers;

        return $this;
    }

    public function getTime(): ?int
    {
        return $this->time;
    }

    public function setTime(int $time): self
    {
        $this->time = $time;

        return $this;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getChapter(): ?chapter
    {
        return $this->chapter;
    }

    public function setChapter(?chapter $chapter): self
    {
        $this->chapter = $chapter;

        return $this;
    }

    /**
     * @return Collection<int, Holes>
     */
    public function getHoles(): Collection
    {
        return $this->holes;
    }

    public function addHole(Holes $hole): self
    {
        if (!$this->holes->contains($hole)) {
            $this->holes[] = $hole;
            $hole->setFillgap($this);
        }

        return $this;
    }

    public function removeHole(Holes $hole): self
    {
        if ($this->holes->removeElement($hole)) {
            // set the owning side to null (unless already changed)
            if ($hole->getFillgap() === $this) {
                $hole->setFillgap(null);
            }
        }

        return $this;
    }

    public function getExtra(): ?string
    {
        return $this->extra;
    }

    public function setExtra(?string $extra): self
    {
        $this->extra = $extra;

        return $this;
    }
}
