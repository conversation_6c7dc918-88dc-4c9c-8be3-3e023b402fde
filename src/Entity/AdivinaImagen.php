<?php

namespace App\Entity;

use App\Repository\AdivinaImagenRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=AdivinaImagenRepository::class)
 * @Vich\Uploadable()
 */
class AdivinaImagen
{
    use Imageable;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"roulette","adivinaImagen"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"roulette","adivinaImagen"})
     */
    private $image;

    /**
     * @Vich\UploadableField(mapping="image_game_adivinaImagen", fileNameProperty="image")
     */
    private $imageFile;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"roulette","adivinaImagen"})
     */
    private $words;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups({"roulette","adivinaImagen"})
     */
    private $clue;

    /**
     * @ORM\Column(type="integer")
     * @Groups({"roulette","adivinaImagen"})
     */
    private $time;

    /**
     * @ORM\ManyToOne(targetEntity=Chapter::class, inversedBy="adivinaImagens")
     */
    private $chapter;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"roulette","adivinaImagen"})
     */
    private $title;


    public function __clone()
    {
        $this->id = null;

        // clone image
        if ($this->getUploadsFolder()) {
            $this->cloneImage();
        }
    }

	public function __toString() {
		return $this->title;
	}

	public function getId(): ?int
    {
        return $this->id;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(?string $image): self
    {
        $this->image = $image;

        return $this;
    }

    public function getWords(): ?string
    {
        return $this->words;
    }

    public function setWords(string $words): self
    {
        $this->words = $words;

        return $this;
    }

    public function getClue(): ?string
    {
        return $this->clue;
    }

    public function setClue(?string $clue): self
    {
        $this->clue = $clue;

        return $this;
    }

    public function getTime(): ?int
    {
        return $this->time;
    }

    public function setTime(int $time): self
    {
        $this->time = $time;

        return $this;
    }

    public function getChapter(): ?Chapter
    {
        return $this->chapter;
    }

    public function setChapter(?Chapter $chapter): self
    {
        $this->chapter = $chapter;

        return $this;
    }

    /**
     * @param mixed $imageFile
     * @return mixed
     */
    public function setImageFile($imageFile): self
    {
        $this->imageFile = $imageFile;
        return $this;
    }

    public function getImageFile()
    {
        return $this->imageFile;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;

        return $this;
    }
}
